/* FlowLens Web Application Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Code Editor Styles */
.CodeMirror {
    border: 1px solid #ced4da;
    border-radius: var(--border-radius);
    font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
    font-size: 14px;
    height: 400px;
}

.CodeMirror-focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* File Input Styling */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Enhancements */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Loading States */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Results Display */
#resultsContent {
    max-height: 600px;
    overflow-y: auto;
}

#resultImage {
    max-width: 100%;
    height: auto;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Mermaid Diagram Styling */
#mermaidDiagram {
    text-align: center;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    overflow: auto;
}

#mermaidDiagram svg {
    max-width: 100%;
    height: auto;
}

/* Text Content Display */
#textDisplay pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    font-size: 0.875rem;
    max-height: 400px;
    overflow: auto;
}

/* Tables */
.table {
    font-size: 0.875rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

.table-responsive {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
}

/* Stats Cards */
.card.bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
}

.card.bg-light .card-body h3 {
    margin-bottom: 0.25rem;
    font-weight: 700;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

/* Tier Status Styling */
#tierStatus {
    font-weight: 600;
}

.tier-free {
    color: #6c757d;
}

.tier-pro {
    color: #ffc107;
}

.tier-team {
    color: #198754;
}

/* Empty State */
#emptyState i {
    opacity: 0.5;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .CodeMirror {
        height: 300px;
    }
    
    #resultsContent {
        max-height: 400px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* Code Syntax Highlighting */
.hljs {
    background: #f8f9fa;
    color: #333;
}

.hljs-keyword {
    color: #0066cc;
    font-weight: bold;
}

.hljs-string {
    color: #009900;
}

.hljs-comment {
    color: #999;
    font-style: italic;
}

.hljs-number {
    color: #cc6600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
