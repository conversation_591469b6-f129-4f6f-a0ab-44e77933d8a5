"""
FlowLens Step Viewer

This module provides step-through visualization capabilities for
interactive code execution analysis.

Features:
- Step-by-step execution visualization
- Variable state display at each step
- Interactive navigation through execution
- Code highlighting and annotation
"""

from typing import Dict, List, Optional, Any
from pathlib import Path
import matplotlib.pyplot as plt
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.syntax import Syntax

from ..core.execution_tracker import ExecutionStep, ExecutionResult
from .renderers import BaseRenderer

console = Console()


class StepViewer(BaseRenderer):
    """
    Interactive step-through viewer for code execution.
    
    Provides visualization and navigation capabilities for
    step-by-step code execution analysis.
    """
    
    def __init__(self):
        super().__init__()
        self.current_step = 0
        self.execution_steps: List[ExecutionStep] = []
        self.source_code = ""
        self.show_variables = True
        self.show_stack_trace = False
        
    def load_execution_result(self, result: ExecutionResult, source_code: str) -> None:
        """Load execution result for step-through viewing."""
        self.execution_steps = result.steps
        self.source_code = source_code
        self.current_step = 0
    
    def render(self, data: Any, output_path: Optional[Path] = None) -> Optional[Any]:
        """Render the current step view."""
        if not self.execution_steps:
            console.print("[red]No execution steps loaded[/red]")
            return None
        
        step = self.execution_steps[self.current_step]
        self._display_step(step)
        
        return step
    
    def next_step(self) -> Optional[ExecutionStep]:
        """Move to the next execution step."""
        if self.current_step < len(self.execution_steps) - 1:
            self.current_step += 1
            return self.execution_steps[self.current_step]
        return None
    
    def previous_step(self) -> Optional[ExecutionStep]:
        """Move to the previous execution step."""
        if self.current_step > 0:
            self.current_step -= 1
            return self.execution_steps[self.current_step]
        return None
    
    def jump_to_step(self, step_number: int) -> Optional[ExecutionStep]:
        """Jump to a specific step number."""
        if 0 <= step_number < len(self.execution_steps):
            self.current_step = step_number
            return self.execution_steps[self.current_step]
        return None
    
    def jump_to_line(self, line_number: int) -> Optional[ExecutionStep]:
        """Jump to the first step at a specific line number."""
        for i, step in enumerate(self.execution_steps):
            if step.line_number == line_number:
                self.current_step = i
                return step
        return None
    
    def get_current_step(self) -> Optional[ExecutionStep]:
        """Get the current execution step."""
        if 0 <= self.current_step < len(self.execution_steps):
            return self.execution_steps[self.current_step]
        return None
    
    def create_view(self, execution_data: Any) -> Dict[str, Any]:
        """Create a step view data structure."""
        if isinstance(execution_data, ExecutionResult):
            return {
                "total_steps": len(execution_data.steps),
                "current_step": self.current_step,
                "execution_id": execution_data.execution_id,
                "duration_ms": execution_data.duration_ms,
                "final_state": execution_data.final_state.value,
            }
        return {}
    
    def _display_step(self, step: ExecutionStep) -> None:
        """Display a single execution step."""
        # Create step header
        header = f"Step {step.step_id} | Line {step.line_number} | {step.execution_time_ms:.2f}ms"
        
        # Display code context
        self._display_code_context(step)
        
        # Display step information
        step_info = f"""
Step: {step.step_id}
Line: {step.line_number}
Statement: {step.statement}
Time: {step.execution_time_ms:.2f}ms
        """.strip()
        
        console.print(Panel(step_info, title=header, border_style="blue"))
        
        # Display variables if enabled
        if self.show_variables and step.all_variables:
            self._display_variables(step.all_variables)
        
        # Display stack trace if enabled
        if self.show_stack_trace and step.stack_trace:
            self._display_stack_trace(step.stack_trace)
    
    def _display_code_context(self, step: ExecutionStep, context_lines: int = 3) -> None:
        """Display code context around the current line."""
        if not self.source_code:
            return
        
        lines = self.source_code.splitlines()
        current_line = step.line_number - 1  # Convert to 0-based index
        
        # Calculate context range
        start_line = max(0, current_line - context_lines)
        end_line = min(len(lines), current_line + context_lines + 1)
        
        # Create syntax-highlighted code
        context_code = "\n".join(lines[start_line:end_line])
        
        # Highlight current line
        syntax = Syntax(
            context_code,
            "python",
            line_numbers=True,
            start_line=start_line + 1,
            highlight_lines={step.line_number},
            theme="monokai"
        )
        
        console.print(Panel(syntax, title="Code Context", border_style="green"))
    
    def _display_variables(self, variables: Dict[str, Any]) -> None:
        """Display variable states."""
        if not variables:
            return
        
        # Filter out dunder variables
        filtered_vars = {k: v for k, v in variables.items() if not k.startswith('__')}
        
        if not filtered_vars:
            return
        
        table = Table(title="Variables")
        table.add_column("Name", style="cyan")
        table.add_column("Value", style="green")
        table.add_column("Type", style="yellow")
        
        for name, value in filtered_vars.items():
            # Truncate long values
            value_str = str(value)
            if len(value_str) > 50:
                value_str = value_str[:47] + "..."
            
            table.add_row(name, value_str, type(value).__name__)
        
        console.print(table)
    
    def _display_stack_trace(self, stack_trace: List[str]) -> None:
        """Display stack trace information."""
        if not stack_trace:
            return
        
        trace_text = "\n".join(stack_trace)
        console.print(Panel(trace_text, title="Stack Trace", border_style="red"))
    
    def get_step_summary(self) -> Dict[str, Any]:
        """Get summary of current step."""
        if not self.execution_steps:
            return {}
        
        step = self.execution_steps[self.current_step]
        
        return {
            "step_id": step.step_id,
            "line_number": step.line_number,
            "statement": step.statement,
            "execution_time_ms": step.execution_time_ms,
            "variable_count": len(step.all_variables),
            "has_stack_trace": bool(step.stack_trace),
            "progress": f"{self.current_step + 1}/{len(self.execution_steps)}",
        }
    
    def export_step_data(self, output_path: Path) -> None:
        """Export current step data to file."""
        if not self.execution_steps:
            raise ValueError("No execution steps loaded")
        
        step = self.execution_steps[self.current_step]
        
        step_data = {
            "step_id": step.step_id,
            "line_number": step.line_number,
            "statement": step.statement,
            "timestamp": step.timestamp,
            "execution_time_ms": step.execution_time_ms,
            "local_variables": step.local_vars,
            "global_variables": step.global_vars,
            "stack_trace": step.stack_trace,
        }
        
        import json
        with open(output_path, 'w') as f:
            json.dump(step_data, f, indent=2, default=str)
    
    def create_step_visualization(self, output_path: Optional[Path] = None) -> Optional[plt.Figure]:
        """Create a visual representation of the execution step."""
        if not self.execution_steps:
            return None
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # Plot execution timeline
        steps = list(range(len(self.execution_steps)))
        times = [step.execution_time_ms for step in self.execution_steps]
        
        ax1.plot(steps, times, 'b-', alpha=0.7)
        ax1.axvline(x=self.current_step, color='red', linestyle='--', linewidth=2)
        ax1.set_xlabel('Step Number')
        ax1.set_ylabel('Execution Time (ms)')
        ax1.set_title('Execution Timeline')
        ax1.grid(True, alpha=0.3)
        
        # Plot variable count over time
        var_counts = [len(step.all_variables) for step in self.execution_steps]
        
        ax2.plot(steps, var_counts, 'g-', alpha=0.7)
        ax2.axvline(x=self.current_step, color='red', linestyle='--', linewidth=2)
        ax2.set_xlabel('Step Number')
        ax2.set_ylabel('Variable Count')
        ax2.set_title('Variable Count Over Time')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if output_path:
            fig.savefig(output_path, dpi=100, bbox_inches='tight')
            plt.close(fig)
            return None
        else:
            return fig
