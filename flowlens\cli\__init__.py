"""
FlowLens CLI Module

This module provides the command-line interface for FlowLens,
including interactive commands and user interaction handling.

Features:
- Main CLI entry point
- Command definitions and parsing
- Interactive mode handling
- User interface components
"""

from .main import main, cli
from .commands import analyze_command, step_command, graph_command, heatmap_command
from .interactive import InteractiveSession

__all__ = [
    "main",
    "cli", 
    "analyze_command",
    "step_command",
    "graph_command",
    "heatmap_command",
    "InteractiveSession",
]
