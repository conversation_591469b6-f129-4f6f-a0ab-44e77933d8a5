"""
FlowLens Analytics and Telemetry

This module provides usage analytics, telemetry tracking, and conversion
funnel monitoring for the freemium business model.

Features:
- Feature usage tracking
- Session duration monitoring
- Conversion funnel analytics
- Performance metrics collection
- Privacy-compliant data collection
"""

import time
import uuid
import json
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from collections import defaultdict
import threading
from datetime import datetime, timedelta


@dataclass
class AnalyticsEvent:
    """Represents a single analytics event."""
    
    event_id: str
    user_id: str
    session_id: str
    event_type: str
    event_name: str
    timestamp: float
    properties: Dict[str, Any]
    duration_ms: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)


@dataclass
class SessionInfo:
    """Information about a user session."""
    
    session_id: str
    user_id: str
    start_time: float
    last_activity: float
    events_count: int = 0
    features_used: set = None
    
    def __post_init__(self):
        if self.features_used is None:
            self.features_used = set()
    
    @property
    def duration_seconds(self) -> float:
        """Get session duration in seconds."""
        return self.last_activity - self.start_time
    
    @property
    def is_active(self) -> bool:
        """Check if session is still active (within 30 minutes)."""
        return time.time() - self.last_activity < 1800  # 30 minutes


class AnalyticsCollector:
    """
    Collects and manages analytics data for FlowLens.
    
    Provides privacy-compliant telemetry collection to understand
    user behavior and optimize the freemium conversion funnel.
    """
    
    def __init__(self, enabled: bool = True, anonymous: bool = True):
        self.enabled = enabled
        self.anonymous = anonymous
        self._user_id = self._generate_user_id()
        self._session_id = self._generate_session_id()
        self._session_start = time.time()
        self._events: List[AnalyticsEvent] = []
        self._sessions: Dict[str, SessionInfo] = {}
        self._feature_usage: Dict[str, int] = defaultdict(int)
        self._conversion_funnel: Dict[str, int] = defaultdict(int)
        self._lock = threading.Lock()
        
        # Initialize current session
        self._current_session = SessionInfo(
            session_id=self._session_id,
            user_id=self._user_id,
            start_time=self._session_start,
            last_activity=self._session_start
        )
        self._sessions[self._session_id] = self._current_session
    
    def _generate_user_id(self) -> str:
        """Generate anonymous user ID."""
        if self.anonymous:
            # Use machine-specific but anonymous identifier
            import platform
            machine_info = f"{platform.node()}-{platform.machine()}-{platform.system()}"
            return hashlib.sha256(machine_info.encode()).hexdigest()[:16]
        else:
            return str(uuid.uuid4())
    
    def _generate_session_id(self) -> str:
        """Generate session ID."""
        return str(uuid.uuid4())[:8]
    
    def track_event(self, event_type: str, event_name: str, 
                   properties: Optional[Dict[str, Any]] = None,
                   duration_ms: Optional[float] = None) -> None:
        """
        Track an analytics event.
        
        Args:
            event_type: Category of event (e.g., 'feature_usage', 'conversion')
            event_name: Specific event name (e.g., 'analyze_code', 'export_png')
            properties: Additional event properties
            duration_ms: Event duration in milliseconds
        """
        if not self.enabled:
            return
        
        with self._lock:
            event = AnalyticsEvent(
                event_id=str(uuid.uuid4())[:8],
                user_id=self._user_id,
                session_id=self._session_id,
                event_type=event_type,
                event_name=event_name,
                timestamp=time.time(),
                properties=properties or {},
                duration_ms=duration_ms
            )
            
            self._events.append(event)
            self._current_session.events_count += 1
            self._current_session.last_activity = time.time()
            self._current_session.features_used.add(event_name)
            
            # Update feature usage counters
            self._feature_usage[event_name] += 1
            
            # Track conversion funnel
            if event_type == 'conversion':
                self._conversion_funnel[event_name] += 1
    
    def track_feature_usage(self, feature_name: str, properties: Optional[Dict[str, Any]] = None) -> None:
        """Track feature usage."""
        self.track_event('feature_usage', feature_name, properties)
    
    def track_conversion_event(self, event_name: str, properties: Optional[Dict[str, Any]] = None) -> None:
        """Track conversion funnel events."""
        self.track_event('conversion', event_name, properties)
    
    def track_performance(self, operation: str, duration_ms: float, 
                         properties: Optional[Dict[str, Any]] = None) -> None:
        """Track performance metrics."""
        self.track_event('performance', operation, properties, duration_ms)
    
    def track_error(self, error_type: str, error_message: str, 
                   properties: Optional[Dict[str, Any]] = None) -> None:
        """Track errors and exceptions."""
        error_properties = {
            'error_type': error_type,
            'error_message': error_message[:200],  # Truncate long messages
            **(properties or {})
        }
        self.track_event('error', 'exception_occurred', error_properties)
    
    def track_upgrade_prompt(self, prompt_type: str, feature_attempted: str,
                           user_action: str = 'shown') -> None:
        """Track upgrade prompt interactions."""
        properties = {
            'prompt_type': prompt_type,
            'feature_attempted': feature_attempted,
            'user_action': user_action
        }
        self.track_event('conversion', f'upgrade_prompt_{user_action}', properties)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get current session summary."""
        return {
            'session_id': self._session_id,
            'user_id': self._user_id,
            'duration_seconds': self._current_session.duration_seconds,
            'events_count': self._current_session.events_count,
            'features_used': list(self._current_session.features_used),
            'unique_features': len(self._current_session.features_used)
        }
    
    def get_feature_usage_stats(self) -> Dict[str, int]:
        """Get feature usage statistics."""
        return dict(self._feature_usage)
    
    def get_conversion_funnel_stats(self) -> Dict[str, int]:
        """Get conversion funnel statistics."""
        return dict(self._conversion_funnel)
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get comprehensive analytics summary."""
        return {
            'session_summary': self.get_session_summary(),
            'feature_usage': self.get_feature_usage_stats(),
            'conversion_funnel': self.get_conversion_funnel_stats(),
            'total_events': len(self._events),
            'analytics_enabled': self.enabled,
            'anonymous_mode': self.anonymous
        }
    
    def export_events(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Export events for analysis."""
        events = self._events[-limit:] if limit else self._events
        return [event.to_dict() for event in events]
    
    def clear_data(self) -> None:
        """Clear all collected analytics data."""
        with self._lock:
            self._events.clear()
            self._feature_usage.clear()
            self._conversion_funnel.clear()
            self._sessions.clear()
    
    def save_to_file(self, filepath: Path) -> None:
        """Save analytics data to file."""
        data = {
            'summary': self.get_analytics_summary(),
            'events': self.export_events(),
            'export_timestamp': time.time()
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)


# Global analytics collector instance
_analytics_collector: Optional[AnalyticsCollector] = None


def get_analytics_collector() -> AnalyticsCollector:
    """Get the global analytics collector instance."""
    global _analytics_collector
    if _analytics_collector is None:
        # Check if analytics is enabled via environment variable
        import os
        enabled = os.environ.get('FLOWLENS_ANALYTICS', 'true').lower() in ('true', '1', 'yes')
        anonymous = os.environ.get('FLOWLENS_ANONYMOUS', 'true').lower() in ('true', '1', 'yes')
        _analytics_collector = AnalyticsCollector(enabled=enabled, anonymous=anonymous)
    return _analytics_collector


def track_feature_usage(feature_name: str, properties: Optional[Dict[str, Any]] = None) -> None:
    """Convenience function to track feature usage."""
    get_analytics_collector().track_feature_usage(feature_name, properties)


def track_conversion_event(event_name: str, properties: Optional[Dict[str, Any]] = None) -> None:
    """Convenience function to track conversion events."""
    get_analytics_collector().track_conversion_event(event_name, properties)


def track_performance(operation: str, duration_ms: float, 
                     properties: Optional[Dict[str, Any]] = None) -> None:
    """Convenience function to track performance."""
    get_analytics_collector().track_performance(operation, duration_ms, properties)


def track_error(error_type: str, error_message: str, 
               properties: Optional[Dict[str, Any]] = None) -> None:
    """Convenience function to track errors."""
    get_analytics_collector().track_error(error_type, error_message, properties)


def track_upgrade_prompt(prompt_type: str, feature_attempted: str,
                        user_action: str = 'shown') -> None:
    """Convenience function to track upgrade prompts."""
    get_analytics_collector().track_upgrade_prompt(prompt_type, feature_attempted, user_action)


# Context manager for tracking operation duration
class track_operation:
    """Context manager for tracking operation performance."""
    
    def __init__(self, operation_name: str, properties: Optional[Dict[str, Any]] = None):
        self.operation_name = operation_name
        self.properties = properties or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = (time.time() - self.start_time) * 1000
            
            if exc_type:
                # Track error if exception occurred
                track_error(exc_type.__name__, str(exc_val), self.properties)
            else:
                # Track successful operation
                track_performance(self.operation_name, duration_ms, self.properties)
