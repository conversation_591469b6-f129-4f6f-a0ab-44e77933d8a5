"""
FlowLens Flow Diagram Renderer

This module provides control flow graph visualization using matplotlib,
networkx, and graphviz. It creates professional-quality diagrams showing
code execution flow and control structures.

Key Features:
- Multiple layout algorithms (hierarchical, circular, spring)
- Customizable themes and styling
- High-quality output formats (PNG, SVG, PDF)
- Interactive and static rendering modes
- Performance optimized for large graphs
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import networkx as nx
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings

# Suppress matplotlib warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

from ..core.cfg_generator import ControlFlowGraph, NodeType
from ..core.analytics import track_feature_usage, track_error
from ..core.licensing import get_license_manager, check_feature_access
from .renderers import BaseRenderer


class FlowDiagramRenderer(BaseRenderer):
    """
    Renders control flow graphs as visual diagrams.
    
    Supports multiple layout algorithms, themes, and output formats
    for creating professional control flow visualizations.
    """
    
    def __init__(self):
        super().__init__()
        self.layout_algorithm = "hierarchical"
        self.theme = "default"
        self.node_colors = self._get_default_node_colors()
        self.edge_colors = self._get_default_edge_colors()
        self.figure_size = (12, 8)
        self.dpi = 100
        self.font_size = 10
        self.node_size_base = 1000
        
    def set_layout(self, layout: str) -> None:
        """Set the layout algorithm."""
        valid_layouts = ["hierarchical", "circular", "spring", "shell", "kamada_kawai"]
        if layout not in valid_layouts:
            raise ValueError(f"Invalid layout: {layout}. Must be one of {valid_layouts}")
        self.layout_algorithm = layout
    
    def set_theme(self, theme: str) -> None:
        """Set the visualization theme."""
        self.theme = theme
        self.node_colors = self._get_theme_node_colors(theme)
        self.edge_colors = self._get_theme_edge_colors(theme)
        
        if theme == "dark":
            plt.style.use('dark_background')
        else:
            plt.style.use('default')
    
    def render(self, cfg: ControlFlowGraph, output_path: Optional[Path] = None) -> Optional[plt.Figure]:
        """
        Render a control flow graph.
        
        Args:
            cfg: Control flow graph to render
            output_path: Optional path to save the image
            
        Returns:
            matplotlib Figure object if no output_path, None otherwise
        """
        if not cfg.nodes:
            raise ValueError("CFG has no nodes to render")
        
        # Create figure
        fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
        
        # Convert CFG to NetworkX graph for layout
        G = self._cfg_to_networkx(cfg)
        
        # Calculate layout positions
        pos = self._calculate_layout(G, cfg)
        
        # Draw nodes
        self._draw_nodes(ax, G, pos, cfg)
        
        # Draw edges
        self._draw_edges(ax, G, pos, cfg)
        
        # Add labels
        self._draw_labels(ax, G, pos, cfg)
        
        # Set title and formatting
        title = f"Control Flow Graph: {cfg.name}"
        ax.set_title(title, fontsize=self.font_size + 4, fontweight='bold')
        
        # Remove axes
        ax.set_axis_off()
        
        # Adjust layout
        plt.tight_layout()
        
        # Save or return
        if output_path:
            self._save_figure(fig, output_path)
            plt.close(fig)
            return None
        else:
            return fig
    
    def render_to_file(self, cfg: ControlFlowGraph, output_path: Path, format: str = "png") -> None:
        """Render CFG to file with licensing checks."""
        if format not in ["png", "svg", "pdf"]:
            raise ValueError(f"Unsupported format: {format}")

        # Check export format licensing
        can_export, upgrade_prompt = check_feature_access('export', {'format': format})
        if not can_export:
            # Track blocked export attempt
            track_feature_usage('export_blocked', {
                'format': format,
                'reason': 'tier_limit'
            })

            # Record the attempt for analytics
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=False)

            raise ValueError(f"Export format '{format}' not available in your tier. {upgrade_prompt.message}")

        # Track successful export attempt
        track_feature_usage('export_started', {
            'format': format,
            'output_type': 'flow_diagram'
        })

        # Ensure output path has correct extension
        if not output_path.suffix:
            output_path = output_path.with_suffix(f".{format}")

        try:
            self.render(cfg, output_path)

            # Record successful export
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=True)

            track_feature_usage('export_completed', {
                'format': format,
                'output_type': 'flow_diagram',
                'file_size_kb': output_path.stat().st_size / 1024 if output_path.exists() else 0
            })

        except Exception as e:
            # Track export error
            track_error('ExportError', str(e), {
                'format': format,
                'output_type': 'flow_diagram'
            })

            # Record failed export
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=False)

            raise
    
    def _cfg_to_networkx(self, cfg: ControlFlowGraph) -> nx.DiGraph:
        """Convert CFG to NetworkX graph."""
        G = nx.DiGraph()
        
        # Add nodes
        for node_id, node in cfg.nodes.items():
            G.add_node(node_id, **{
                'type': node.node_type,
                'line': node.line_number,
                'statement': node.statement,
            })
        
        # Add edges
        for edge in cfg.edges:
            G.add_edge(edge.source, edge.target, **{
                'condition': edge.condition,
                'type': edge.edge_type,
            })
        
        return G
    
    def _calculate_layout(self, G: nx.DiGraph, cfg: ControlFlowGraph) -> Dict[str, Tuple[float, float]]:
        """Calculate node positions using the selected layout algorithm."""
        if self.layout_algorithm == "hierarchical":
            return self._hierarchical_layout(G, cfg)
        elif self.layout_algorithm == "circular":
            return nx.circular_layout(G)
        elif self.layout_algorithm == "spring":
            return nx.spring_layout(G, k=2, iterations=50)
        elif self.layout_algorithm == "shell":
            return nx.shell_layout(G)
        elif self.layout_algorithm == "kamada_kawai":
            return nx.kamada_kawai_layout(G)
        else:
            return nx.spring_layout(G)
    
    def _hierarchical_layout(self, G: nx.DiGraph, cfg: ControlFlowGraph) -> Dict[str, Tuple[float, float]]:
        """Create improved hierarchical layout with better node positioning."""
        pos = {}

        # Use topological sort for better flow representation
        try:
            topo_order = list(nx.topological_sort(G))
        except (nx.NetworkXError, nx.NetworkXUnfeasible):
            # Fallback to line-based ordering if graph has cycles
            topo_order = sorted(cfg.nodes.keys(), key=lambda x: cfg.nodes[x].line_number)

        # Group nodes by their level in the hierarchy
        levels = self._calculate_node_levels(G, cfg, topo_order)

        # Position nodes level by level
        y_spacing = 1.5  # Increased spacing for clarity
        x_spacing = 2.5  # Increased spacing for clarity

        for level, nodes_in_level in levels.items():
            y = -level * y_spacing  # Top to bottom

            # Sort nodes in level by line number for consistency
            nodes_in_level.sort(key=lambda x: cfg.nodes[x].line_number)

            # Center nodes horizontally
            total_width = (len(nodes_in_level) - 1) * x_spacing
            start_x = -total_width / 2

            for j, node_id in enumerate(nodes_in_level):
                x = start_x + j * x_spacing
                pos[node_id] = (x, y)

        # Apply force-directed adjustment for better edge routing
        pos = self._adjust_positions_for_edges(pos, G, cfg)

        return pos

    def _calculate_node_levels(self, G: nx.DiGraph, cfg: ControlFlowGraph,
                              topo_order: List[str]) -> Dict[int, List[str]]:
        """Calculate hierarchical levels for nodes."""
        levels = {}
        node_levels = {}

        # Assign levels based on topological order and dependencies
        for node_id in topo_order:
            # Calculate level based on predecessors
            predecessors = list(G.predecessors(node_id))
            if not predecessors:
                level = 0
            else:
                level = max(node_levels.get(pred, 0) for pred in predecessors) + 1

            node_levels[node_id] = level

            if level not in levels:
                levels[level] = []
            levels[level].append(node_id)

        return levels

    def _adjust_positions_for_edges(self, pos: Dict[str, Tuple[float, float]],
                                   G: nx.DiGraph, cfg: ControlFlowGraph) -> Dict[str, Tuple[float, float]]:
        """Adjust node positions to minimize edge crossings."""
        # Simple adjustment to reduce edge crossings
        adjusted_pos = pos.copy()

        # Group nodes by y-coordinate (level)
        levels = {}
        for node_id, (x, y) in pos.items():
            if y not in levels:
                levels[y] = []
            levels[y].append((node_id, x))

        # Sort nodes within each level to minimize crossings
        for y, nodes_at_level in levels.items():
            if len(nodes_at_level) <= 1:
                continue

            # Calculate barycenter for each node based on connected nodes
            barycenters = []
            for node_id, x in nodes_at_level:
                connected_x = []

                # Consider predecessors and successors
                for pred in G.predecessors(node_id):
                    if pred in pos:
                        connected_x.append(pos[pred][0])

                for succ in G.successors(node_id):
                    if succ in pos:
                        connected_x.append(pos[succ][0])

                if connected_x:
                    barycenter = sum(connected_x) / len(connected_x)
                else:
                    barycenter = x

                barycenters.append((node_id, barycenter))

            # Sort by barycenter and reassign x positions
            barycenters.sort(key=lambda x: x[1])
            x_spacing = 2.5
            total_width = (len(barycenters) - 1) * x_spacing
            start_x = -total_width / 2

            for i, (node_id, _) in enumerate(barycenters):
                new_x = start_x + i * x_spacing
                adjusted_pos[node_id] = (new_x, y)

        return adjusted_pos
    
    def _draw_nodes(self, ax: plt.Axes, G: nx.DiGraph, pos: Dict[str, Tuple[float, float]],
                   cfg: ControlFlowGraph) -> None:
        """Draw nodes with improved styling and shapes."""
        for node_id, (x, y) in pos.items():
            node = cfg.nodes[node_id]

            # Get node color and size
            color = self.node_colors.get(node.node_type, self.node_colors[NodeType.STATEMENT])
            size = self._get_node_size(node)
            edge_color = 'black' if self.theme != 'dark' else 'white'

            # Draw different shapes based on node type
            if node.node_type == NodeType.ENTRY:
                # Rounded rectangle for entry
                rect = patches.FancyBboxPatch((x-size, y-size/2), size*2, size,
                                            boxstyle="round,pad=0.1",
                                            facecolor=color, edgecolor=edge_color,
                                            linewidth=2, alpha=0.9)
                ax.add_patch(rect)

            elif node.node_type == NodeType.EXIT:
                # Rounded rectangle for exit
                rect = patches.FancyBboxPatch((x-size, y-size/2), size*2, size,
                                            boxstyle="round,pad=0.1",
                                            facecolor=color, edgecolor=edge_color,
                                            linewidth=2, alpha=0.9)
                ax.add_patch(rect)

            elif node.node_type == NodeType.CONDITION:
                # Diamond for conditions
                diamond = patches.RegularPolygon((x, y), 4, radius=size*1.2,
                                               orientation=np.pi/4,
                                               facecolor=color, edgecolor=edge_color,
                                               linewidth=2, alpha=0.9)
                ax.add_patch(diamond)

            elif node.node_type == NodeType.LOOP_HEADER:
                # Hexagon for loops
                hexagon = patches.RegularPolygon((x, y), 6, radius=size*1.1,
                                               facecolor=color, edgecolor=edge_color,
                                               linewidth=2, alpha=0.9)
                ax.add_patch(hexagon)

            else:
                # Rectangle for regular statements
                rect = patches.Rectangle((x-size, y-size/2), size*2, size,
                                       facecolor=color, edgecolor=edge_color,
                                       linewidth=1.5, alpha=0.9)
                ax.add_patch(rect)

            # Add improved label
            label = self._create_node_label(node)

            # Adjust font size based on label length and node size
            font_size = min(10, max(6, 80 / max(len(label), 10)))
            text_color = 'white' if self._is_dark_color(color) else 'black'

            # Multi-line text for long labels
            if len(label) > 20:
                # Split long labels into multiple lines
                words = label.split()
                lines = []
                current_line = []
                current_length = 0

                for word in words:
                    if current_length + len(word) + 1 <= 20:
                        current_line.append(word)
                        current_length += len(word) + 1
                    else:
                        if current_line:
                            lines.append(' '.join(current_line))
                        current_line = [word]
                        current_length = len(word)

                if current_line:
                    lines.append(' '.join(current_line))

                label = '\n'.join(lines[:3])  # Limit to 3 lines

            ax.text(x, y, label, ha='center', va='center', fontsize=font_size,
                   fontweight='bold', color=text_color,
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8)
                   if self._is_dark_color(color) else None)
    
    def _draw_edges(self, ax: plt.Axes, G: nx.DiGraph, pos: Dict[str, Tuple[float, float]], 
                   cfg: ControlFlowGraph) -> None:
        """Draw edges with appropriate styling."""
        for edge in cfg.edges:
            if edge.source not in pos or edge.target not in pos:
                continue
            
            x1, y1 = pos[edge.source]
            x2, y2 = pos[edge.target]
            
            # Get edge color and style
            color = self.edge_colors.get(edge.edge_type, 'black')
            style = self._get_edge_style(edge)
            
            # Draw arrow
            ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                       arrowprops=dict(arrowstyle='->', color=color, 
                                     linestyle=style, linewidth=1.5))
            
            # Add condition label if present
            if edge.condition:
                mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
                ax.text(mid_x, mid_y, edge.condition, fontsize=self.font_size - 2,
                       ha='center', va='center', bbox=dict(boxstyle='round,pad=0.2',
                       facecolor='white', alpha=0.8))
    
    def _draw_labels(self, ax: plt.Axes, G: nx.DiGraph, pos: Dict[str, Tuple[float, float]], 
                    cfg: ControlFlowGraph) -> None:
        """Draw node labels."""
        for node_id, (x, y) in pos.items():
            node = cfg.nodes[node_id]
            
            # Create label
            label = self._create_node_label(node)
            
            # Draw label
            ax.text(x, y, label, fontsize=self.font_size, ha='center', va='center',
                   fontweight='bold', color='white' if self.theme == 'dark' else 'black')
    
    def _get_node_size(self, node) -> float:
        """Get node size based on type and content."""
        base_size = 0.3
        
        size_multipliers = {
            NodeType.ENTRY: 1.2,
            NodeType.EXIT: 1.2,
            NodeType.CONDITION: 1.1,
            NodeType.LOOP_HEADER: 1.1,
            NodeType.EXCEPTION_HANDLER: 1.1,
            NodeType.FUNCTION_CALL: 1.0,
            NodeType.STATEMENT: 1.0,
        }
        
        multiplier = size_multipliers.get(node.node_type, 1.0)
        return base_size * multiplier
    
    def _get_edge_style(self, edge) -> str:
        """Get edge line style."""
        if edge.edge_type == "exception":
            return "--"
        elif edge.condition == "False":
            return ":"
        else:
            return "-"
    
    def _create_node_label(self, node) -> str:
        """Create a readable label for a node."""
        if node.node_type == NodeType.ENTRY:
            return "START"
        elif node.node_type == NodeType.EXIT:
            return "END"
        else:
            # Clean up the statement
            statement = node.statement.strip()

            # Remove common Python keywords for cleaner display
            statement = statement.replace('    ', ' ')  # Reduce indentation

            # Truncate long statements intelligently
            if len(statement) > 40:
                # Try to break at logical points
                if ' = ' in statement:
                    parts = statement.split(' = ', 1)
                    if len(parts[0]) < 20:
                        statement = f"{parts[0]} = ..."
                    else:
                        statement = statement[:37] + "..."
                elif '(' in statement and statement.index('(') < 20:
                    statement = statement[:statement.index('(')] + "(...)"
                else:
                    statement = statement[:37] + "..."

            return f"L{node.line_number}: {statement}"

    def _is_dark_color(self, color: str) -> bool:
        """Check if a color is dark (for text color selection)."""
        # Simple heuristic based on color names
        dark_colors = ['darkblue', 'darkred', 'darkgreen', 'purple', 'navy', 'maroon']
        return any(dark in color.lower() for dark in dark_colors)
    
    def _save_figure(self, fig: plt.Figure, output_path: Path) -> None:
        """Save figure to file."""
        format = output_path.suffix[1:].lower()
        
        save_kwargs = {
            'dpi': self.dpi,
            'bbox_inches': 'tight',
            'pad_inches': 0.1,
        }
        
        if format == 'svg':
            save_kwargs['format'] = 'svg'
        elif format == 'pdf':
            save_kwargs['format'] = 'pdf'
        else:
            save_kwargs['format'] = 'png'
        
        fig.savefig(output_path, **save_kwargs)
    
    def _get_default_node_colors(self) -> Dict[NodeType, str]:
        """Get default node colors."""
        return {
            NodeType.ENTRY: '#4CAF50',      # Green
            NodeType.EXIT: '#F44336',       # Red
            NodeType.STATEMENT: '#2196F3',  # Blue
            NodeType.CONDITION: '#FF9800',  # Orange
            NodeType.LOOP_HEADER: '#9C27B0', # Purple
            NodeType.LOOP_BODY: '#E91E63',  # Pink
            NodeType.EXCEPTION_HANDLER: '#FF5722', # Deep Orange
            NodeType.FUNCTION_CALL: '#00BCD4', # Cyan
            NodeType.RETURN: '#795548',     # Brown
        }
    
    def _get_default_edge_colors(self) -> Dict[str, str]:
        """Get default edge colors."""
        return {
            'normal': 'black',
            'exception': 'red',
            'break': 'orange',
            'continue': 'blue',
        }
    
    def _get_theme_node_colors(self, theme: str) -> Dict[NodeType, str]:
        """Get theme-specific node colors."""
        if theme == "dark":
            return {
                NodeType.ENTRY: '#66BB6A',
                NodeType.EXIT: '#EF5350',
                NodeType.STATEMENT: '#42A5F5',
                NodeType.CONDITION: '#FFA726',
                NodeType.LOOP_HEADER: '#AB47BC',
                NodeType.LOOP_BODY: '#EC407A',
                NodeType.EXCEPTION_HANDLER: '#FF7043',
                NodeType.FUNCTION_CALL: '#26C6DA',
                NodeType.RETURN: '#8D6E63',
            }
        elif theme == "light":
            return {
                NodeType.ENTRY: '#A5D6A7',
                NodeType.EXIT: '#FFCDD2',
                NodeType.STATEMENT: '#BBDEFB',
                NodeType.CONDITION: '#FFE0B2',
                NodeType.LOOP_HEADER: '#E1BEE7',
                NodeType.LOOP_BODY: '#F8BBD9',
                NodeType.EXCEPTION_HANDLER: '#FFCCBC',
                NodeType.FUNCTION_CALL: '#B2EBF2',
                NodeType.RETURN: '#D7CCC8',
            }
        else:
            return self._get_default_node_colors()
    
    def _get_theme_edge_colors(self, theme: str) -> Dict[str, str]:
        """Get theme-specific edge colors."""
        if theme == "dark":
            return {
                'normal': 'white',
                'exception': '#FF6B6B',
                'break': '#FFB74D',
                'continue': '#64B5F6',
            }
        else:
            return self._get_default_edge_colors()
