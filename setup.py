#!/usr/bin/env python3
"""
FlowLens - Interactive Python Code Visualization & Execution Analysis
Setup configuration for PyPI distribution
"""

import os
import sys
from setuptools import setup, find_packages

# Ensure we're using Python 3.8+
if sys.version_info < (3, 8):
    sys.exit("FlowLens requires Python 3.8 or higher")

# Read long description from README
def read_long_description():
    """Read the long description from README.md"""
    here = os.path.abspath(os.path.dirname(__file__))
    with open(os.path.join(here, 'README.md'), encoding='utf-8') as f:
        return f.read()

# Read requirements from requirements.txt
def read_requirements(filename):
    """Read requirements from a file"""
    here = os.path.abspath(os.path.dirname(__file__))
    try:
        with open(os.path.join(here, filename), encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        return []

# Package metadata
PACKAGE_NAME = "flowlens"
VERSION = "1.0.0"
DESCRIPTION = "Interactive Python Code Visualization & Execution Analysis"
AUTHOR = "Hector Ta"
AUTHOR_EMAIL = "<EMAIL>"
URL = "https://github.com/HectorTa1989/FlowLens"
LICENSE = "MIT"

# Classifiers for PyPI
CLASSIFIERS = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Debuggers",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Visualization",
    "Topic :: Education",
    "Environment :: Console",
]

# Keywords for PyPI search
KEYWORDS = [
    "python", "ast", "visualization", "debugging", "code-analysis",
    "control-flow", "execution-tracing", "cli", "interactive",
    "matplotlib", "networkx", "graphviz"
]

setup(
    name=PACKAGE_NAME,
    version=VERSION,
    description=DESCRIPTION,
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    author=AUTHOR,
    author_email=AUTHOR_EMAIL,
    url=URL,
    license=LICENSE,
    
    # Package discovery
    packages=find_packages(exclude=["tests*", "docs*", "examples*"]),
    include_package_data=True,
    
    # Python version requirement
    python_requires=">=3.8",
    
    # Dependencies
    install_requires=read_requirements("requirements.txt"),
    
    # Optional dependencies
    extras_require={
        "dev": [
            "pytest>=7.2.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.10.0",
            "pytest-benchmark>=4.0.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "pylint>=2.15.0",
            "bandit>=1.7.4",
            "pre-commit>=3.0.0",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=1.0.0",
            "myst-parser>=0.17.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.10.0",
            "pytest-benchmark>=4.0.0",
        ],
    },
    
    # Entry points for CLI
    entry_points={
        "console_scripts": [
            "flowlens=flowlens.cli.main:main",
            "fl=flowlens.cli.main:main",  # Short alias
        ],
    },
    
    # Package data
    package_data={
        "flowlens": [
            "config/*.json",
            "config/*.yaml",
            "templates/*.html",
            "static/*",
        ],
    },
    
    # Metadata for PyPI
    classifiers=CLASSIFIERS,
    keywords=" ".join(KEYWORDS),
    project_urls={
        "Homepage": URL,
        "Documentation": "https://docs.flowlens.com",
        "Source": URL,
        "Tracker": f"{URL}/issues",
        "Changelog": f"{URL}/blob/main/CHANGELOG.md",
    },
    
    # Zip safety
    zip_safe=False,
    
    # Platform compatibility
    platforms=["any"],
)
