"""
FlowLens Control Flow Graph Generator

This module generates Control Flow Graphs (CFG) from Python AST structures.
CFGs represent the flow of control through a program, showing all possible
execution paths and decision points.

Key Features:
- Generate CFGs from AST parse results
- Handle complex control structures (loops, conditionals, exceptions)
- Support for function calls and returns
- Optimized for visualization and analysis
- NetworkX-based graph representation
"""

import ast
import networkx as nx
from typing import Dict, List, Optional, Set, Tuple, Any, Union
from dataclasses import dataclass, field
from collections import defaultdict
from enum import Enum

from .ast_parser import ParseResult


class NodeType(Enum):
    """Types of CFG nodes."""
    ENTRY = "entry"
    EXIT = "exit"
    STATEMENT = "statement"
    CONDITION = "condition"
    LOOP_HEADER = "loop_header"
    LOOP_BODY = "loop_body"
    EXCEPTION_HANDLER = "exception_handler"
    FUNCTION_CALL = "function_call"
    RETURN = "return"


@dataclass
class CFGNode:
    """Represents a node in the Control Flow Graph."""
    
    id: str
    node_type: NodeType
    line_number: int
    statement: str
    ast_node: Optional[ast.AST] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __hash__(self):
        return hash(self.id)
    
    def __eq__(self, other):
        return isinstance(other, CFGNode) and self.id == other.id


@dataclass
class CFGEdge:
    """Represents an edge in the Control Flow Graph."""
    
    source: str
    target: str
    condition: Optional[str] = None
    edge_type: str = "normal"
    metadata: Dict[str, Any] = field(default_factory=dict)


class ControlFlowGraph:
    """
    Control Flow Graph representation using NetworkX.
    
    Provides methods for graph manipulation, analysis, and export.
    """
    
    def __init__(self, name: str = "CFG"):
        self.name = name
        self.graph = nx.DiGraph()
        self.nodes: Dict[str, CFGNode] = {}
        self.edges: List[CFGEdge] = []
        self.entry_node: Optional[str] = None
        self.exit_nodes: Set[str] = set()
        self._node_counter = 0
    
    def add_node(self, node: CFGNode) -> str:
        """Add a node to the CFG."""
        self.nodes[node.id] = node
        self.graph.add_node(node.id, **{
            'type': node.node_type.value,
            'line': node.line_number,
            'statement': node.statement,
            'metadata': node.metadata,
        })
        return node.id
    
    def add_edge(self, edge: CFGEdge) -> None:
        """Add an edge to the CFG."""
        self.edges.append(edge)
        self.graph.add_edge(edge.source, edge.target, **{
            'condition': edge.condition,
            'type': edge.edge_type,
            'metadata': edge.metadata,
        })
    
    def get_node(self, node_id: str) -> Optional[CFGNode]:
        """Get a node by ID."""
        return self.nodes.get(node_id)
    
    def get_successors(self, node_id: str) -> List[str]:
        """Get successor nodes."""
        return list(self.graph.successors(node_id))
    
    def get_predecessors(self, node_id: str) -> List[str]:
        """Get predecessor nodes."""
        return list(self.graph.predecessors(node_id))
    
    def get_paths(self, start: Optional[str] = None, end: Optional[str] = None) -> List[List[str]]:
        """Get all paths between start and end nodes."""
        start = start or self.entry_node
        if not start:
            return []
        
        if end:
            try:
                return list(nx.all_simple_paths(self.graph, start, end))
            except nx.NetworkXNoPath:
                return []
        else:
            # Get paths to all exit nodes
            paths = []
            for exit_node in self.exit_nodes:
                try:
                    paths.extend(nx.all_simple_paths(self.graph, start, exit_node))
                except nx.NetworkXNoPath:
                    continue
            return paths
    
    def get_complexity_metrics(self) -> Dict[str, int]:
        """Calculate complexity metrics for the CFG."""
        return {
            "nodes": len(self.nodes),
            "edges": len(self.edges),
            "cyclomatic_complexity": len(self.edges) - len(self.nodes) + 2,
            "decision_points": len([n for n in self.nodes.values() 
                                  if n.node_type == NodeType.CONDITION]),
            "loops": len([n for n in self.nodes.values() 
                         if n.node_type == NodeType.LOOP_HEADER]),
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Export CFG to dictionary format."""
        return {
            "name": self.name,
            "nodes": [
                {
                    "id": node.id,
                    "type": node.node_type.value,
                    "line": node.line_number,
                    "statement": node.statement,
                    "metadata": node.metadata,
                }
                for node in self.nodes.values()
            ],
            "edges": [
                {
                    "source": edge.source,
                    "target": edge.target,
                    "condition": edge.condition,
                    "type": edge.edge_type,
                    "metadata": edge.metadata,
                }
                for edge in self.edges
            ],
            "entry_node": self.entry_node,
            "exit_nodes": list(self.exit_nodes),
            "metrics": self.get_complexity_metrics(),
        }


class CFGGenerator:
    """
    Generates Control Flow Graphs from Python AST structures.
    
    Uses a recursive descent approach to build CFGs that accurately
    represent the control flow of Python programs.
    """
    
    def __init__(self):
        self._node_counter = 0
        self._current_cfg: Optional[ControlFlowGraph] = None
        self._break_targets: List[str] = []
        self._continue_targets: List[str] = []
        self._return_nodes: Set[str] = set()
    
    def generate_cfg(self, parse_result: ParseResult, function_name: Optional[str] = None) -> ControlFlowGraph:
        """
        Generate a CFG from AST parse results.
        
        Args:
            parse_result: Result from AST parsing
            function_name: Optional specific function to analyze
            
        Returns:
            ControlFlowGraph representing the code structure
        """
        if not parse_result.ast_tree:
            raise ValueError("Parse result must contain AST tree")
        
        cfg_name = function_name or parse_result.filepath or "main"
        self._current_cfg = ControlFlowGraph(cfg_name)
        self._node_counter = 0
        self._break_targets.clear()
        self._continue_targets.clear()
        self._return_nodes.clear()
        
        # Create entry node
        entry_node = self._create_node(NodeType.ENTRY, 0, "ENTRY")
        self._current_cfg.entry_node = entry_node.id
        
        # Process the AST
        if function_name:
            # Find specific function
            target_function = self._find_function(parse_result.ast_tree, function_name)
            if target_function:
                last_node = self._process_function_body(target_function, entry_node.id)
            else:
                raise ValueError(f"Function '{function_name}' not found")
        else:
            # Process entire module
            last_node = self._process_statements(parse_result.ast_tree.body, entry_node.id)
        
        # Create exit node if needed
        if last_node and last_node not in self._current_cfg.exit_nodes:
            exit_node = self._create_node(NodeType.EXIT, 0, "EXIT")
            self._current_cfg.exit_nodes.add(exit_node.id)
            self._add_edge(last_node, exit_node.id)
        
        # Add return nodes to exit nodes
        self._current_cfg.exit_nodes.update(self._return_nodes)
        
        return self._current_cfg
    
    def _create_node(self, node_type: NodeType, line_number: int, statement: str, 
                     ast_node: Optional[ast.AST] = None) -> CFGNode:
        """Create a new CFG node."""
        self._node_counter += 1
        node = CFGNode(
            id=f"node_{self._node_counter}",
            node_type=node_type,
            line_number=line_number,
            statement=statement,
            ast_node=ast_node,
        )
        self._current_cfg.add_node(node)
        return node
    
    def _add_edge(self, source: str, target: str, condition: Optional[str] = None, 
                  edge_type: str = "normal") -> None:
        """Add an edge to the CFG."""
        edge = CFGEdge(source, target, condition, edge_type)
        self._current_cfg.add_edge(edge)
    
    def _process_statements(self, statements: List[ast.stmt], entry_node: str) -> Optional[str]:
        """Process a list of statements."""
        current_node = entry_node
        
        for stmt in statements:
            current_node = self._process_statement(stmt, current_node)
            if current_node is None:
                break
        
        return current_node
    
    def _process_statement(self, stmt: ast.stmt, entry_node: str) -> Optional[str]:
        """Process a single statement."""
        if isinstance(stmt, ast.If):
            return self._process_if(stmt, entry_node)
        elif isinstance(stmt, ast.While):
            return self._process_while(stmt, entry_node)
        elif isinstance(stmt, ast.For):
            return self._process_for(stmt, entry_node)
        elif isinstance(stmt, ast.Try):
            return self._process_try(stmt, entry_node)
        elif isinstance(stmt, ast.With):
            return self._process_with(stmt, entry_node)
        elif isinstance(stmt, ast.Return):
            return self._process_return(stmt, entry_node)
        elif isinstance(stmt, ast.Break):
            return self._process_break(stmt, entry_node)
        elif isinstance(stmt, ast.Continue):
            return self._process_continue(stmt, entry_node)
        elif isinstance(stmt, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
            # Skip function/class definitions in main flow
            return entry_node
        else:
            # Regular statement
            return self._process_simple_statement(stmt, entry_node)
    
    def _process_if(self, stmt: ast.If, entry_node: str) -> str:
        """Process if statement."""
        # Create condition node
        condition_text = self._get_statement_text(stmt.test)
        condition_node = self._create_node(
            NodeType.CONDITION, 
            stmt.lineno, 
            f"if {condition_text}",
            stmt
        )
        self._add_edge(entry_node, condition_node.id)
        
        # Process if body
        if_exit = self._process_statements(stmt.body, condition_node.id)
        self._add_edge(condition_node.id, 
                      self._get_first_node_in_statements(stmt.body) or condition_node.id,
                      "True")
        
        # Process else body
        else_exit = None
        if stmt.orelse:
            else_exit = self._process_statements(stmt.orelse, condition_node.id)
            self._add_edge(condition_node.id,
                          self._get_first_node_in_statements(stmt.orelse) or condition_node.id,
                          "False")
        
        # Create merge node
        merge_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "merge")
        
        if if_exit:
            self._add_edge(if_exit, merge_node.id)
        if else_exit:
            self._add_edge(else_exit, merge_node.id)
        elif not stmt.orelse:
            # No else branch, condition can flow directly to merge
            self._add_edge(condition_node.id, merge_node.id, "False")
        
        return merge_node.id
    
    def _process_while(self, stmt: ast.While, entry_node: str) -> str:
        """Process while loop."""
        # Create loop header
        condition_text = self._get_statement_text(stmt.test)
        header_node = self._create_node(
            NodeType.LOOP_HEADER,
            stmt.lineno,
            f"while {condition_text}",
            stmt
        )
        self._add_edge(entry_node, header_node.id)
        
        # Create exit node for loop
        exit_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "loop_exit")
        
        # Set up break/continue targets
        old_break = self._break_targets.copy()
        old_continue = self._continue_targets.copy()
        self._break_targets.append(exit_node.id)
        self._continue_targets.append(header_node.id)
        
        # Process loop body
        body_exit = self._process_statements(stmt.body, header_node.id)
        self._add_edge(header_node.id,
                      self._get_first_node_in_statements(stmt.body) or header_node.id,
                      "True")
        
        # Body flows back to header
        if body_exit:
            self._add_edge(body_exit, header_node.id)
        
        # Header can exit loop
        self._add_edge(header_node.id, exit_node.id, "False")
        
        # Process else clause
        if stmt.orelse:
            else_exit = self._process_statements(stmt.orelse, exit_node.id)
            if else_exit:
                exit_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "while_else_exit")
                self._add_edge(else_exit, exit_node.id)
        
        # Restore break/continue targets
        self._break_targets = old_break
        self._continue_targets = old_continue
        
        return exit_node.id
    
    def _process_for(self, stmt: ast.For, entry_node: str) -> str:
        """Process for loop."""
        # Create loop header
        target_text = self._get_statement_text(stmt.target)
        iter_text = self._get_statement_text(stmt.iter)
        header_node = self._create_node(
            NodeType.LOOP_HEADER,
            stmt.lineno,
            f"for {target_text} in {iter_text}",
            stmt
        )
        self._add_edge(entry_node, header_node.id)
        
        # Create exit node
        exit_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "for_exit")
        
        # Set up break/continue targets
        old_break = self._break_targets.copy()
        old_continue = self._continue_targets.copy()
        self._break_targets.append(exit_node.id)
        self._continue_targets.append(header_node.id)
        
        # Process loop body
        body_exit = self._process_statements(stmt.body, header_node.id)
        self._add_edge(header_node.id,
                      self._get_first_node_in_statements(stmt.body) or header_node.id,
                      "iterate")
        
        # Body flows back to header
        if body_exit:
            self._add_edge(body_exit, header_node.id)
        
        # Header can exit loop
        self._add_edge(header_node.id, exit_node.id, "done")
        
        # Process else clause
        if stmt.orelse:
            else_exit = self._process_statements(stmt.orelse, exit_node.id)
            if else_exit:
                exit_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "for_else_exit")
                self._add_edge(else_exit, exit_node.id)
        
        # Restore break/continue targets
        self._break_targets = old_break
        self._continue_targets = old_continue
        
        return exit_node.id
    
    def _process_try(self, stmt: ast.Try, entry_node: str) -> str:
        """Process try-except statement."""
        # Process try body
        try_exit = self._process_statements(stmt.body, entry_node)
        
        # Create merge node for all paths
        merge_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "try_merge")
        
        if try_exit:
            self._add_edge(try_exit, merge_node.id)
        
        # Process exception handlers
        for handler in stmt.handlers:
            handler_node = self._create_node(
                NodeType.EXCEPTION_HANDLER,
                handler.lineno,
                f"except {self._get_exception_type(handler)}",
                handler
            )
            # Exception can occur at any point in try block
            self._add_edge(entry_node, handler_node.id, "exception")
            
            handler_exit = self._process_statements(handler.body, handler_node.id)
            if handler_exit:
                self._add_edge(handler_exit, merge_node.id)
        
        # Process else clause
        if stmt.orelse:
            else_exit = self._process_statements(stmt.orelse, try_exit or entry_node)
            if else_exit:
                self._add_edge(else_exit, merge_node.id)
        
        # Process finally clause
        if stmt.finalbody:
            finally_node = self._create_node(
                NodeType.STATEMENT,
                stmt.finalbody[0].lineno,
                "finally",
            )
            self._add_edge(merge_node.id, finally_node.id)
            finally_exit = self._process_statements(stmt.finalbody, finally_node.id)
            return finally_exit or finally_node.id
        
        return merge_node.id
    
    def _process_with(self, stmt: ast.With, entry_node: str) -> str:
        """Process with statement."""
        with_node = self._create_node(
            NodeType.STATEMENT,
            stmt.lineno,
            f"with {len(stmt.items)} items",
            stmt
        )
        self._add_edge(entry_node, with_node.id)
        
        body_exit = self._process_statements(stmt.body, with_node.id)
        return body_exit or with_node.id
    
    def _process_return(self, stmt: ast.Return, entry_node: str) -> None:
        """Process return statement."""
        return_text = "return"
        if stmt.value:
            return_text += f" {self._get_statement_text(stmt.value)}"
        
        return_node = self._create_node(
            NodeType.RETURN,
            stmt.lineno,
            return_text,
            stmt
        )
        self._add_edge(entry_node, return_node.id)
        self._return_nodes.add(return_node.id)
        return None  # No continuation after return
    
    def _process_break(self, stmt: ast.Break, entry_node: str) -> None:
        """Process break statement."""
        break_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "break", stmt)
        self._add_edge(entry_node, break_node.id)
        
        if self._break_targets:
            self._add_edge(break_node.id, self._break_targets[-1], "break")
        
        return None  # No continuation after break
    
    def _process_continue(self, stmt: ast.Continue, entry_node: str) -> None:
        """Process continue statement."""
        continue_node = self._create_node(NodeType.STATEMENT, stmt.lineno, "continue", stmt)
        self._add_edge(entry_node, continue_node.id)
        
        if self._continue_targets:
            self._add_edge(continue_node.id, self._continue_targets[-1], "continue")
        
        return None  # No continuation after continue
    
    def _process_simple_statement(self, stmt: ast.stmt, entry_node: str) -> str:
        """Process a simple statement."""
        statement_text = self._get_statement_text(stmt)
        stmt_node = self._create_node(
            NodeType.STATEMENT,
            stmt.lineno,
            statement_text,
            stmt
        )
        self._add_edge(entry_node, stmt_node.id)
        return stmt_node.id
    
    # Helper methods
    def _find_function(self, tree: ast.AST, function_name: str) -> Optional[ast.FunctionDef]:
        """Find a function definition by name."""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name == function_name:
                return node
        return None
    
    def _process_function_body(self, func: ast.FunctionDef, entry_node: str) -> Optional[str]:
        """Process function body statements."""
        return self._process_statements(func.body, entry_node)
    
    def _get_first_node_in_statements(self, statements: List[ast.stmt]) -> Optional[str]:
        """Get the first CFG node ID for a list of statements."""
        # This is a simplified implementation
        # In practice, you'd need to track the first node created for each statement
        return None
    
    def _get_statement_text(self, node: ast.AST) -> str:
        """Get text representation of an AST node."""
        try:
            if hasattr(ast, 'unparse'):  # Python 3.9+
                return ast.unparse(node)
            else:
                # Fallback for older Python versions
                if isinstance(node, ast.Name):
                    return node.id
                elif isinstance(node, ast.Constant):
                    return repr(node.value)
                elif isinstance(node, ast.BinOp):
                    return f"{self._get_statement_text(node.left)} {self._get_op_text(node.op)} {self._get_statement_text(node.right)}"
                else:
                    return f"<{type(node).__name__}>"
        except Exception:
            return f"<{type(node).__name__}>"
    
    def _get_op_text(self, op: ast.operator) -> str:
        """Get text representation of an operator."""
        op_map = {
            ast.Add: '+', ast.Sub: '-', ast.Mult: '*', ast.Div: '/',
            ast.Mod: '%', ast.Pow: '**', ast.LShift: '<<', ast.RShift: '>>',
            ast.BitOr: '|', ast.BitXor: '^', ast.BitAnd: '&', ast.FloorDiv: '//',
        }
        return op_map.get(type(op), '?')
    
    def _get_exception_type(self, handler: ast.ExceptHandler) -> str:
        """Get exception type name."""
        if handler.type:
            return self._get_statement_text(handler.type)
        return "Exception"
