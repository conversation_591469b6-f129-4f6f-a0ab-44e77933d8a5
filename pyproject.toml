[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "flowlens"
version = "1.0.0"
description = "Interactive Python Code Visualization & Execution Analysis"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Hector Ta", email = "<EMAIL>"}
]
maintainers = [
    {name = "Hector Ta", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Debuggers",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Visualization",
    "Topic :: Education",
    "Environment :: Console",
]
keywords = [
    "python", "ast", "visualization", "debugging", "code-analysis",
    "control-flow", "execution-tracing", "cli", "interactive"
]
requires-python = ">=3.8"
dependencies = [
    "click>=8.1.0",
    "rich>=13.0.0",
    "colorama>=0.4.6",
    "networkx>=3.0",
    "matplotlib>=3.6.0",
    "graphviz>=0.20.0",
    "numpy>=1.21.0",
    "pandas>=1.5.0",
    "psutil>=5.9.0",
    "memory-profiler>=0.60.0",
    "pyyaml>=6.0",
    "toml>=0.10.2",
    "jsonschema>=4.0.0",
    "prompt-toolkit>=3.0.0",
    "pygments>=2.13.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.2.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-benchmark>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pylint>=2.15.0",
    "pre-commit>=3.0.0",
]
docs = [
    "sphinx>=5.3.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=0.18.0",
    "sphinx-click>=4.4.0",
]
test = [
    "pytest>=7.2.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-benchmark>=4.0.0",
]

[project.scripts]
flowlens = "flowlens.cli.main:main"
fl = "flowlens.cli.main:main"

[project.urls]
Homepage = "https://github.com/HectorTa1989/FlowLens"
Documentation = "https://docs.flowlens.com"
Repository = "https://github.com/HectorTa1989/FlowLens"
Issues = "https://github.com/HectorTa1989/FlowLens/issues"
Changelog = "https://github.com/HectorTa1989/FlowLens/blob/main/CHANGELOG.md"

[tool.setuptools]
packages = ["flowlens"]

[tool.setuptools.package-data]
flowlens = ["config/*.json", "config/*.yaml", "templates/*.html", "static/*"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["flowlens"]

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "networkx.*",
    "matplotlib.*",
    "graphviz.*",
    "memory_profiler.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "benchmark: marks tests as benchmark tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["flowlens"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
