"""
Sample Python code for FlowLens testing.

This module contains various Python constructs to test
FlowLens parsing, analysis, and visualization capabilities.
"""

import math
import random
from typing import List, Dict, Optional


class Calculator:
    """A simple calculator class for testing."""
    
    def __init__(self):
        self.history: List[float] = []
        self.memory: float = 0.0
    
    def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        result = a + b
        self.history.append(result)
        return result
    
    def subtract(self, a: float, b: float) -> float:
        """Subtract two numbers."""
        result = a - b
        self.history.append(result)
        return result
    
    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        result = a * b
        self.history.append(result)
        return result
    
    def divide(self, a: float, b: float) -> float:
        """Divide two numbers."""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        result = a / b
        self.history.append(result)
        return result
    
    def power(self, base: float, exponent: float) -> float:
        """Calculate power."""
        result = math.pow(base, exponent)
        self.history.append(result)
        return result
    
    def clear_history(self) -> None:
        """Clear calculation history."""
        self.history.clear()
    
    def get_history(self) -> List[float]:
        """Get calculation history."""
        return self.history.copy()


def fibonacci(n: int) -> List[int]:
    """Generate Fibonacci sequence up to n terms."""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    sequence = [0, 1]
    for i in range(2, n):
        next_num = sequence[i-1] + sequence[i-2]
        sequence.append(next_num)
    
    return sequence


def factorial(n: int) -> int:
    """Calculate factorial using recursion."""
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    elif n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n - 1)


def is_prime(n: int) -> bool:
    """Check if a number is prime."""
    if n < 2:
        return False
    elif n == 2:
        return True
    elif n % 2 == 0:
        return False
    
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        if n % i == 0:
            return False
    
    return True


def find_primes(limit: int) -> List[int]:
    """Find all prime numbers up to limit."""
    primes = []
    
    for num in range(2, limit + 1):
        if is_prime(num):
            primes.append(num)
    
    return primes


def bubble_sort(arr: List[int]) -> List[int]:
    """Sort array using bubble sort algorithm."""
    n = len(arr)
    sorted_arr = arr.copy()
    
    for i in range(n):
        swapped = False
        
        for j in range(0, n - i - 1):
            if sorted_arr[j] > sorted_arr[j + 1]:
                sorted_arr[j], sorted_arr[j + 1] = sorted_arr[j + 1], sorted_arr[j]
                swapped = True
        
        if not swapped:
            break
    
    return sorted_arr


def binary_search(arr: List[int], target: int) -> int:
    """Search for target in sorted array using binary search."""
    left, right = 0, len(arr) - 1
    
    while left <= right:
        mid = (left + right) // 2
        
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    
    return -1


def process_data(data: List[Dict[str, any]]) -> Dict[str, any]:
    """Process a list of data dictionaries."""
    result = {
        'count': len(data),
        'total': 0,
        'average': 0,
        'max_value': None,
        'min_value': None,
    }
    
    if not data:
        return result
    
    values = []
    
    for item in data:
        if 'value' in item and isinstance(item['value'], (int, float)):
            values.append(item['value'])
    
    if values:
        result['total'] = sum(values)
        result['average'] = result['total'] / len(values)
        result['max_value'] = max(values)
        result['min_value'] = min(values)
    
    return result


def simulate_random_walk(steps: int, start_position: float = 0.0) -> List[float]:
    """Simulate a random walk."""
    positions = [start_position]
    current_position = start_position
    
    for _ in range(steps):
        # Random step: -1, 0, or 1
        step = random.choice([-1, 0, 1])
        current_position += step
        positions.append(current_position)
    
    return positions


def main():
    """Main function demonstrating various operations."""
    print("FlowLens Sample Code Execution")
    print("=" * 40)
    
    # Calculator operations
    calc = Calculator()
    
    try:
        result1 = calc.add(10, 5)
        result2 = calc.multiply(result1, 2)
        result3 = calc.divide(result2, 3)
        
        print(f"Calculator results: {calc.get_history()}")
        
    except ValueError as e:
        print(f"Calculator error: {e}")
    
    # Fibonacci sequence
    fib_sequence = fibonacci(10)
    print(f"Fibonacci(10): {fib_sequence}")
    
    # Factorial calculation
    try:
        fact_5 = factorial(5)
        print(f"Factorial(5): {fact_5}")
    except ValueError as e:
        print(f"Factorial error: {e}")
    
    # Prime numbers
    primes = find_primes(20)
    print(f"Primes up to 20: {primes}")
    
    # Sorting
    unsorted_list = [64, 34, 25, 12, 22, 11, 90]
    sorted_list = bubble_sort(unsorted_list)
    print(f"Sorted list: {sorted_list}")
    
    # Binary search
    target = 25
    index = binary_search(sorted_list, target)
    if index != -1:
        print(f"Found {target} at index {index}")
    else:
        print(f"{target} not found")
    
    # Data processing
    sample_data = [
        {'name': 'item1', 'value': 10},
        {'name': 'item2', 'value': 20},
        {'name': 'item3', 'value': 15},
        {'name': 'item4', 'value': 30},
    ]
    
    stats = process_data(sample_data)
    print(f"Data statistics: {stats}")
    
    # Random walk simulation
    walk = simulate_random_walk(10)
    print(f"Random walk: {walk}")
    
    print("Execution completed successfully!")


if __name__ == "__main__":
    main()
