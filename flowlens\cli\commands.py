"""
FlowLens CLI Commands

This module implements the individual CLI commands for FlowLens,
providing the core functionality for code analysis, visualization,
and interactive debugging.

Features:
- Code analysis and reporting
- Step-through execution
- Control flow graph generation
- Variable heatmap visualization
- Interactive REPL mode
"""

import json
import time
from pathlib import Path
from typing import Optional

import click
from rich.console import <PERSON>sole
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.syntax import Syntax

from ..core import ASTParser, CFGGenerator, ExecutionTracker, VariableMonitor
from ..core.analytics import track_feature_usage, get_analytics_collector
from ..core.licensing import get_license_manager, check_feature_access
from ..core.architecture_analyzer import ArchitectureAnalyzer
from ..visualization import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HeatmapRenderer
from ..visualization.mermaid_renderer import MermaidRenderer
from ..utils.validators import InputValidator, CodeValidator
from ..utils.file_handler import FileHandler, CodeLoader

console = Console()


def display_upgrade_prompt(upgrade_prompt) -> None:
    """Display upgrade prompt to user."""
    from rich.panel import Panel
    from rich.text import Text

    # Create upgrade prompt content
    content = Text()
    content.append(f"{upgrade_prompt.message}\n\n", style="yellow")

    content.append("Benefits of upgrading:\n", style="bold cyan")
    for benefit in upgrade_prompt.benefits:
        content.append(f"  ✓ {benefit}\n", style="green")

    content.append(f"\n{upgrade_prompt.call_to_action}", style="bold blue")
    content.append(f"\nRecommended tier: {upgrade_prompt.recommended_tier.value.title()}", style="magenta")

    # Display in a panel
    panel = Panel(
        content,
        title=f"🚀 {upgrade_prompt.title}",
        border_style="yellow",
        padding=(1, 2)
    )

    console.print(panel)
    console.print("\n[dim]Visit https://flowlens.com/pricing for more information[/dim]")


def display_tier_info() -> None:
    """Display current tier information."""
    license_manager = get_license_manager()
    tier_info = license_manager.get_tier_info()

    from rich.table import Table

    table = Table(title="FlowLens Tier Information")
    table.add_column("Property", style="cyan")
    table.add_column("Value", style="green")

    table.add_row("Current Tier", tier_info['current_tier'].title())

    limits = tier_info['limits']
    table.add_row("Max File Lines", str(limits['max_file_lines']) if limits['max_file_lines'] else "Unlimited")
    table.add_row("Export Formats", ", ".join(limits['export_formats']))
    table.add_row("Performance Profiling", "✓" if limits['performance_profiling'] else "✗")
    table.add_row("Custom Themes", "✓" if limits['custom_themes'] else "✗")

    usage = tier_info['session_usage']
    table.add_row("Files Analyzed (Session)", str(usage['files_analyzed']))
    table.add_row("Session Duration", f"{usage['session_duration_minutes']:.1f} minutes")

    console.print(table)


def analyze_command(ctx, filepath: Path, function: Optional[str], output: Optional[Path],
                   output_format: str, performance: bool) -> int:
    """Analyze Python code and generate comprehensive report."""
    try:
        # Track command usage
        track_feature_usage('cli_analyze_started', {
            'has_function_filter': function is not None,
            'has_output': output is not None,
            'output_format': output_format,
            'performance_enabled': performance
        })

        # Initialize components
        validator = InputValidator()
        code_validator = CodeValidator()
        file_handler = FileHandler()
        parser = ASTParser(enable_performance_monitoring=performance)
        
        # Validate input file
        path_result = validator.validate_file_path(filepath)
        if not path_result.is_valid:
            console.print(f"[red]Error:[/red] {'; '.join(path_result.errors)}")
            return 1
        
        # Load and validate code
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Loading file...", total=None)
            
            content, file_info = file_handler.load_file(filepath)
            
            if not file_info.is_valid_python:
                console.print(f"[red]Invalid Python code:[/red] {file_info.syntax_error}")
                return 1
            
            progress.update(task, description="Validating code...")
            code_result = code_validator.validate_code_string(content)
            
            if not code_result.is_valid:
                console.print(f"[red]Code validation failed:[/red] {'; '.join(code_result.errors)}")
                return 1
            
            if code_result.warnings:
                console.print(f"[yellow]Warnings:[/yellow] {'; '.join(code_result.warnings)}")
            
            progress.update(task, description="Parsing AST...")
            parse_result = parser.parse_code(content, str(filepath))
        
        # Display analysis results
        _display_analysis_results(parse_result, function)
        
        # Save output if requested
        if output:
            _save_analysis_output(parse_result, output, output_format)
            console.print(f"[green]Analysis saved to:[/green] {output}")
        
        # Track successful completion
        track_feature_usage('cli_analyze_completed', {
            'functions_found': len(parse_result.functions),
            'classes_found': len(parse_result.classes),
            'complexity_score': parse_result.complexity_score
        })

        return 0

    except ValueError as e:
        # Handle licensing errors specifically
        if "File analysis blocked" in str(e):
            console.print(f"[yellow]Analysis blocked:[/yellow] {e}")

            # Show tier information
            console.print("\n[blue]Current tier information:[/blue]")
            display_tier_info()

            # Get and display upgrade prompt
            license_manager = get_license_manager()
            file_lines = len(Path(filepath).read_text().splitlines()) if filepath.exists() else 0
            upgrade_prompt = license_manager.get_upgrade_prompt('file_size_limit', {'file_lines': file_lines})
            display_upgrade_prompt(upgrade_prompt)

            return 2  # Special exit code for licensing issues
        else:
            console.print(f"[red]Analysis failed:[/red] {e}")
            return 1

    except Exception as e:
        console.print(f"[red]Analysis failed:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


def step_command(ctx, filepath: Path, function: Optional[str], max_steps: int, auto_run: bool) -> int:
    """Interactive step-through execution of Python code."""
    try:
        # Initialize components
        file_handler = FileHandler()
        parser = ASTParser()
        cfg_generator = CFGGenerator()
        tracker = ExecutionTracker()
        
        # Load code
        content, file_info = file_handler.load_file(filepath)
        if not file_info.is_valid_python:
            console.print(f"[red]Invalid Python code:[/red] {file_info.syntax_error}")
            return 1
        
        # Parse and generate CFG
        parse_result = parser.parse_code(content, str(filepath))
        cfg = cfg_generator.generate_cfg(parse_result, function)
        
        # Start execution tracking
        execution_id = tracker.start_tracking(content, cfg)
        
        console.print(f"[blue]Starting step-through execution of {filepath}[/blue]")
        if function:
            console.print(f"[blue]Focusing on function: {function}[/blue]")
        
        step_count = 0
        
        if auto_run:
            # Auto-run mode
            with Progress() as progress:
                task = progress.add_task("Executing...", total=max_steps)
                
                while step_count < max_steps:
                    step = tracker.step_execute(execution_id)
                    if step is None:
                        break
                    
                    step_count += 1
                    progress.update(task, advance=1)
                    
                    if step_count % 100 == 0:
                        console.print(f"Step {step_count}: Line {step.line_number}")
        else:
            # Interactive mode
            while step_count < max_steps:
                step = tracker.step_execute(execution_id)
                if step is None:
                    console.print("[green]Execution completed[/green]")
                    break
                
                step_count += 1
                _display_execution_step(step, step_count)
                
                # Get user input
                user_input = console.input("[blue]Press Enter to continue, 'q' to quit, 'v' for variables: [/blue]")
                
                if user_input.lower() == 'q':
                    break
                elif user_input.lower() == 'v':
                    _display_step_variables(step)
        
        # Finalize execution
        result = tracker.stop_execution(execution_id)
        if result:
            console.print(f"[green]Execution completed in {result.duration_ms:.2f}ms with {result.total_steps} steps[/green]")
        
        return 0
        
    except Exception as e:
        console.print(f"[red]Step execution failed:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


def graph_command(ctx, filepath: Path, function: Optional[str], output: Optional[Path],
                 output_format: str, theme: str, layout: str) -> int:
    """Generate control flow graph visualization."""
    try:
        # Track command usage
        track_feature_usage('cli_graph_started', {
            'has_function_filter': function is not None,
            'has_output': output is not None,
            'output_format': output_format,
            'theme': theme,
            'layout': layout
        })

        # Initialize components
        file_handler = FileHandler()
        parser = ASTParser()
        cfg_generator = CFGGenerator()
        renderer = FlowDiagramRenderer()
        
        # Load and parse code
        content, file_info = file_handler.load_file(filepath)
        if not file_info.is_valid_python:
            console.print(f"[red]Invalid Python code:[/red] {file_info.syntax_error}")
            return 1
        
        parse_result = parser.parse_code(content, str(filepath))
        
        # Generate CFG
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Generating control flow graph...", total=None)
            cfg = cfg_generator.generate_cfg(parse_result, function)
            
            progress.update(task, description="Rendering visualization...")
            
            # Configure renderer
            renderer.set_theme(theme)
            renderer.set_layout(layout)
            
            # Generate visualization
            if output:
                renderer.render_to_file(cfg, output, output_format)
                console.print(f"[green]Control flow graph saved to:[/green] {output}")
            else:
                # Display in terminal (simplified)
                _display_cfg_summary(cfg)
        
        # Track successful completion
        track_feature_usage('cli_graph_completed', {
            'output_format': output_format,
            'theme': theme,
            'layout': layout,
            'has_output': output is not None
        })

        return 0

    except ValueError as e:
        # Handle export format restrictions
        if "not available in your tier" in str(e):
            console.print(f"[yellow]Export blocked:[/yellow] {e}")

            # Get and display upgrade prompt
            license_manager = get_license_manager()
            upgrade_prompt = license_manager.get_upgrade_prompt('export_format', {'format': output_format})
            display_upgrade_prompt(upgrade_prompt)

            return 2  # Special exit code for licensing issues
        else:
            console.print(f"[red]Graph generation failed:[/red] {e}")
            return 1

    except Exception as e:
        console.print(f"[red]Graph generation failed:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


def heatmap_command(ctx, filepath: Path, function: Optional[str], variable: Optional[str], 
                   output: Optional[Path], output_format: str, colormap: str) -> int:
    """Generate variable state heatmap visualization."""
    try:
        # Initialize components
        file_handler = FileHandler()
        parser = ASTParser()
        cfg_generator = CFGGenerator()
        tracker = ExecutionTracker()
        monitor = VariableMonitor()
        renderer = HeatmapRenderer()
        
        # Load and parse code
        content, file_info = file_handler.load_file(filepath)
        if not file_info.is_valid_python:
            console.print(f"[red]Invalid Python code:[/red] {file_info.syntax_error}")
            return 1
        
        parse_result = parser.parse_code(content, str(filepath))
        cfg = cfg_generator.generate_cfg(parse_result, function)
        
        # Execute and track variables
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Executing code...", total=None)
            
            execution_id = tracker.start_tracking(content, cfg)
            execution_result = tracker.run_to_completion(execution_id)
            
            progress.update(task, description="Analyzing variables...")
            
            monitor.start_monitoring()
            variable_analysis = monitor.process_execution_result(execution_result)
            
            progress.update(task, description="Generating heatmap...")
            
            # Configure renderer
            renderer.set_colormap(colormap)
            
            # Generate heatmap
            heatmap_data = variable_analysis['heatmap_data']
            
            if variable and variable in heatmap_data:
                # Focus on specific variable
                data = {variable: heatmap_data[variable]}
            else:
                # All variables
                data = heatmap_data
            
            if output:
                renderer.render_to_file(data, output, output_format)
                console.print(f"[green]Variable heatmap saved to:[/green] {output}")
            else:
                # Display summary in terminal
                _display_heatmap_summary(data)
        
        return 0
        
    except Exception as e:
        console.print(f"[red]Heatmap generation failed:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


def mermaid_command(ctx, filepath: Path, function: Optional[str], output: Optional[Path],
                   output_format: str, diagram_type: str, theme: str, direction: str) -> int:
    """Generate Mermaid diagram for code visualization."""
    try:
        # Track command usage
        track_feature_usage('cli_mermaid_started', {
            'has_function_filter': function is not None,
            'has_output': output is not None,
            'output_format': output_format,
            'diagram_type': diagram_type,
            'theme': theme,
            'direction': direction
        })

        # Initialize components
        file_handler = FileHandler()
        parser = ASTParser()
        renderer = MermaidRenderer()

        # Configure renderer
        renderer.set_theme(theme)
        renderer.set_direction(direction)

        # Load and parse code
        content, file_info = file_handler.load_file(filepath)
        if not file_info.is_valid_python:
            console.print(f"[red]Invalid Python code:[/red] {file_info.syntax_error}")
            return 1

        parse_result = parser.parse_code(content, str(filepath))

        # Generate appropriate diagram
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            if diagram_type == "flowchart":
                task = progress.add_task("Generating Mermaid flowchart...", total=None)
                cfg_generator = CFGGenerator()
                cfg = cfg_generator.generate_cfg(parse_result, function)

                title = f"Control Flow: {filepath.name}"
                if function:
                    title += f" - {function}()"

                mermaid_content = renderer.render_flowchart(cfg, title)

            elif diagram_type == "class":
                task = progress.add_task("Generating Mermaid class diagram...", total=None)
                title = f"Class Structure: {filepath.name}"
                mermaid_content = renderer.render_class_diagram(parse_result, title)

            else:
                console.print(f"[red]Unsupported diagram type:[/red] {diagram_type}")
                return 1

        # Output result
        if output:
            # Save to file
            renderer.render_to_file(mermaid_content, output, output_format)
            console.print(f"[green]Mermaid diagram saved to:[/green] {output}")
        else:
            # Display in terminal
            console.print("\n[bold cyan]Generated Mermaid Diagram:[/bold cyan]")
            console.print(f"[dim]```mermaid[/dim]")
            console.print(mermaid_content)
            console.print(f"[dim]```[/dim]")

            console.print(f"\n[blue]Tip:[/blue] Save to file with --output option")
            console.print(f"[blue]Tip:[/blue] Render online at https://mermaid.live/")

        # Track successful completion
        track_feature_usage('cli_mermaid_completed', {
            'diagram_type': diagram_type,
            'output_format': output_format,
            'theme': theme,
            'has_output': output is not None,
            'content_length': len(mermaid_content)
        })

        return 0

    except ValueError as e:
        # Handle export format restrictions
        if "not available in your tier" in str(e):
            console.print(f"[yellow]Export blocked:[/yellow] {e}")

            # Get and display upgrade prompt
            license_manager = get_license_manager()
            upgrade_prompt = license_manager.get_upgrade_prompt('export_format', {'format': output_format})
            display_upgrade_prompt(upgrade_prompt)

            return 2  # Special exit code for licensing issues
        else:
            console.print(f"[red]Mermaid generation failed:[/red] {e}")
            return 1

    except Exception as e:
        console.print(f"[red]Mermaid generation failed:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


def architecture_command(ctx, directory: Path, output: Optional[Path],
                        output_format: str, include_patterns: Optional[str],
                        exclude_patterns: Optional[str]) -> int:
    """Analyze and visualize system architecture."""
    try:
        # Track command usage
        track_feature_usage('cli_architecture_started', {
            'has_output': output is not None,
            'output_format': output_format,
            'has_include_patterns': include_patterns is not None,
            'has_exclude_patterns': exclude_patterns is not None
        })

        # Parse patterns
        include_list = include_patterns.split(',') if include_patterns else None
        exclude_list = exclude_patterns.split(',') if exclude_patterns else None

        # Initialize analyzer
        analyzer = ArchitectureAnalyzer()

        # Analyze directory
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Analyzing system architecture...", total=None)
            analysis = analyzer.analyze_directory(directory, include_list, exclude_list)

        # Display results
        _display_architecture_results(analysis)

        # Generate visualization if requested
        if output:
            renderer = MermaidRenderer()

            # Create architecture overview
            parse_results = [module.parse_result for module in analysis.modules.values()]
            title = f"System Architecture: {directory.name}"
            mermaid_content = renderer.render_architecture_overview(parse_results, title)

            # Save to file
            renderer.render_to_file(mermaid_content, output, output_format)
            console.print(f"[green]Architecture diagram saved to:[/green] {output}")

        # Track successful completion
        track_feature_usage('cli_architecture_completed', {
            'modules_analyzed': len(analysis.modules),
            'dependencies_found': len(analysis.dependency_graph),
            'circular_dependencies': len(analysis.circular_dependencies),
            'has_output': output is not None
        })

        return 0

    except ValueError as e:
        # Handle export format restrictions
        if "not available in your tier" in str(e):
            console.print(f"[yellow]Export blocked:[/yellow] {e}")

            # Get and display upgrade prompt
            license_manager = get_license_manager()
            upgrade_prompt = license_manager.get_upgrade_prompt('export_format', {'format': output_format})
            display_upgrade_prompt(upgrade_prompt)

            return 2  # Special exit code for licensing issues
        else:
            console.print(f"[red]Architecture analysis failed:[/red] {e}")
            return 1

    except Exception as e:
        console.print(f"[red]Architecture analysis failed:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


def repl_command(ctx, history_size: int) -> int:
    """Start interactive REPL mode for code analysis."""
    try:
        from .interactive import InteractiveSession
        
        console.print("[blue]Starting FlowLens Interactive REPL[/blue]")
        console.print("Type 'help' for available commands, 'exit' to quit")
        
        session = InteractiveSession(history_size=history_size)
        session.run()
        
        return 0
        
    except Exception as e:
        console.print(f"[red]REPL failed:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


# Helper functions for display

def _display_analysis_results(parse_result, function: Optional[str]) -> None:
    """Display analysis results in a formatted table."""
    summary = parse_result.summary
    
    # Create summary table
    table = Table(title="Code Analysis Summary")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("File", summary.get('filepath', 'N/A'))
    table.add_row("Total Lines", str(summary.get('total_lines', 0)))
    table.add_row("Functions", str(summary.get('functions', 0)))
    table.add_row("Classes", str(summary.get('classes', 0)))
    table.add_row("Decision Points", str(summary.get('decision_points', 0)))
    table.add_row("Loops", str(summary.get('loops', 0)))
    table.add_row("Complexity Score", str(summary.get('complexity_score', 0)))
    table.add_row("Parse Time", f"{summary.get('parse_time_ms', 0):.2f}ms")
    
    console.print(table)
    
    # Display functions if any
    if parse_result.functions:
        func_table = Table(title="Functions Found")
        func_table.add_column("Name", style="cyan")
        func_table.add_column("Line", style="yellow")
        func_table.add_column("Args", style="green")
        func_table.add_column("Complexity", style="red")
        
        for func in parse_result.functions:
            func_table.add_row(
                func['name'],
                str(func['lineno']),
                str(len(func.get('args', []))),
                str(func.get('complexity', 1))
            )
        
        console.print(func_table)


def _display_execution_step(step, step_count: int) -> None:
    """Display a single execution step."""
    panel_content = f"""
Step {step_count}
Line {step.line_number}: {step.statement}
Variables: {len(step.all_variables)}
Time: {step.execution_time_ms:.2f}ms
    """.strip()
    
    console.print(Panel(panel_content, title=f"Execution Step {step_count}"))


def _display_step_variables(step) -> None:
    """Display variables for a step."""
    if not step.all_variables:
        console.print("[yellow]No variables to display[/yellow]")
        return
    
    var_table = Table(title=f"Variables at Step {step.step_id}")
    var_table.add_column("Name", style="cyan")
    var_table.add_column("Value", style="green")
    var_table.add_column("Type", style="yellow")
    
    for name, value in step.all_variables.items():
        if not name.startswith('__'):  # Skip dunder variables
            var_table.add_row(name, str(value)[:50], type(value).__name__)
    
    console.print(var_table)


def _display_architecture_results(analysis) -> None:
    """Display architecture analysis results."""
    from rich.table import Table
    from rich.panel import Panel

    # Main metrics table
    metrics_table = Table(title="System Architecture Metrics")
    metrics_table.add_column("Metric", style="cyan")
    metrics_table.add_column("Value", style="green")

    metrics = analysis.complexity_metrics
    metrics_table.add_row("Total Modules", str(metrics['total_modules']))
    metrics_table.add_row("Total Dependencies", str(metrics['total_dependencies']))
    metrics_table.add_row("Lines of Code", str(metrics['total_lines_of_code']))
    metrics_table.add_row("Avg Dependencies/Module", f"{metrics['average_dependencies_per_module']:.1f}")
    metrics_table.add_row("Coupling Factor", f"{metrics['coupling_factor']:.3f}")
    metrics_table.add_row("Total Complexity", str(metrics['total_complexity_score']))

    console.print(metrics_table)

    # Modules table
    if analysis.modules:
        modules_table = Table(title="Module Overview")
        modules_table.add_column("Module", style="cyan")
        modules_table.add_column("Classes", style="green")
        modules_table.add_column("Functions", style="yellow")
        modules_table.add_column("Dependencies", style="red")
        modules_table.add_column("Dependents", style="blue")

        for module_name, module_info in list(analysis.modules.items())[:10]:  # Show first 10
            modules_table.add_row(
                module_name,
                str(len(module_info.classes)),
                str(len(module_info.functions)),
                str(len(module_info.dependencies)),
                str(len(module_info.dependents))
            )

        if len(analysis.modules) > 10:
            modules_table.add_row("...", "...", "...", "...", "...")

        console.print(modules_table)

    # Circular dependencies warning
    if analysis.circular_dependencies:
        circular_text = "\n".join([" → ".join(cycle) for cycle in analysis.circular_dependencies[:5]])
        if len(analysis.circular_dependencies) > 5:
            circular_text += f"\n... and {len(analysis.circular_dependencies) - 5} more"

        circular_panel = Panel(
            circular_text,
            title="⚠️  Circular Dependencies Detected",
            border_style="red"
        )
        console.print(circular_panel)

    # Entry points
    if analysis.entry_points:
        entry_text = "\n".join(analysis.entry_points[:5])
        if len(analysis.entry_points) > 5:
            entry_text += f"\n... and {len(analysis.entry_points) - 5} more"

        entry_panel = Panel(
            entry_text,
            title="🚀 Entry Points",
            border_style="green"
        )
        console.print(entry_panel)


def _display_cfg_summary(cfg) -> None:
    """Display CFG summary in terminal."""
    metrics = cfg.get_complexity_metrics()
    
    table = Table(title="Control Flow Graph Summary")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    for key, value in metrics.items():
        table.add_row(key.replace('_', ' ').title(), str(value))
    
    console.print(table)


def _display_heatmap_summary(heatmap_data) -> None:
    """Display heatmap summary in terminal."""
    if not heatmap_data:
        console.print("[yellow]No variable data to display[/yellow]")
        return
    
    table = Table(title="Variable Activity Summary")
    table.add_column("Variable", style="cyan")
    table.add_column("Total Accesses", style="green")
    table.add_column("Total Modifications", style="yellow")
    table.add_column("Lines Used", style="blue")
    
    for var_name, data in heatmap_data.items():
        table.add_row(
            var_name,
            str(data.total_accesses),
            str(data.total_modifications),
            str(len(data.line_numbers))
        )
    
    console.print(table)


def _save_analysis_output(parse_result, output_path: Path, format: str) -> None:
    """Save analysis output to file."""
    data = {
        "summary": parse_result.summary,
        "functions": parse_result.functions,
        "classes": parse_result.classes,
        "imports": parse_result.imports,
        "decision_points": parse_result.decision_points,
        "loops": parse_result.loops,
        "variables": list(parse_result.variables),
    }
    
    if format == 'json':
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    elif format == 'yaml':
        import yaml
        with open(output_path, 'w') as f:
            yaml.dump(data, f, default_flow_style=False)
    else:  # text
        with open(output_path, 'w') as f:
            f.write(f"FlowLens Analysis Report\n")
            f.write(f"========================\n\n")
            f.write(f"Summary:\n{json.dumps(data['summary'], indent=2)}\n\n")
            f.write(f"Functions: {len(data['functions'])}\n")
            f.write(f"Classes: {len(data['classes'])}\n")
            f.write(f"Variables: {len(data['variables'])}\n")
