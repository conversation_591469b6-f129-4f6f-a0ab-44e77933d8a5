"""
FlowLens Settings Management

This module provides configuration settings management for FlowLens,
including default values, user preferences, and environment overrides.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class VisualizationSettings:
    """Visualization-specific settings."""
    theme: str = "default"
    dpi: int = 100
    figsize: tuple = (12, 8)
    font_size: int = 10
    max_nodes: int = 1000
    colormap: str = "viridis"
    show_grid: bool = True
    show_colorbar: bool = True


@dataclass
class PerformanceSettings:
    """Performance-related settings."""
    max_latency_ms: float = 200.0
    enable_caching: bool = True
    cache_size: int = 100
    max_file_size_mb: float = 10.0
    enable_monitoring: bool = True


@dataclass
class ExecutionSettings:
    """Execution tracking settings."""
    max_steps: int = 10000
    track_variables: bool = True
    accuracy_threshold: float = 0.95
    enable_step_callbacks: bool = True
    save_execution_history: bool = True


@dataclass
class Settings:
    """Main FlowLens settings container."""
    visualization: VisualizationSettings = None
    performance: PerformanceSettings = None
    execution: ExecutionSettings = None
    
    def __post_init__(self):
        if self.visualization is None:
            self.visualization = VisualizationSettings()
        if self.performance is None:
            self.performance = PerformanceSettings()
        if self.execution is None:
            self.execution = ExecutionSettings()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Settings':
        """Create settings from dictionary."""
        viz_data = data.get('visualization', {})
        perf_data = data.get('performance', {})
        exec_data = data.get('execution', {})
        
        return cls(
            visualization=VisualizationSettings(**viz_data),
            performance=PerformanceSettings(**perf_data),
            execution=ExecutionSettings(**exec_data)
        )


class SettingsManager:
    """Manages FlowLens settings with persistence."""
    
    def __init__(self):
        self._settings = Settings()
        self._config_dir = self._get_config_dir()
        self._config_file = self._config_dir / "settings.json"
        self._load_settings()
    
    def _get_config_dir(self) -> Path:
        """Get configuration directory."""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '~')) / 'FlowLens'
        else:  # Unix-like
            config_dir = Path.home() / '.config' / 'flowlens'
        
        config_dir.mkdir(parents=True, exist_ok=True)
        return config_dir
    
    def _load_settings(self) -> None:
        """Load settings from file."""
        if self._config_file.exists():
            try:
                with open(self._config_file, 'r') as f:
                    data = json.load(f)
                self._settings = Settings.from_dict(data)
            except (json.JSONDecodeError, KeyError, TypeError):
                # Use defaults if config is corrupted
                self._settings = Settings()
    
    def save_settings(self) -> None:
        """Save settings to file."""
        try:
            with open(self._config_file, 'w') as f:
                json.dump(self._settings.to_dict(), f, indent=2)
        except OSError:
            # Ignore save errors
            pass
    
    def get_settings(self) -> Settings:
        """Get current settings."""
        return self._settings
    
    def update_settings(self, **kwargs) -> None:
        """Update settings with new values."""
        for key, value in kwargs.items():
            if hasattr(self._settings, key):
                if isinstance(value, dict):
                    # Update nested settings
                    current_value = getattr(self._settings, key)
                    for nested_key, nested_value in value.items():
                        if hasattr(current_value, nested_key):
                            setattr(current_value, nested_key, nested_value)
                else:
                    setattr(self._settings, key, value)
        
        self.save_settings()
    
    def reset_settings(self) -> None:
        """Reset settings to defaults."""
        self._settings = Settings()
        self.save_settings()
    
    def get_config_path(self) -> Path:
        """Get configuration file path."""
        return self._config_file


# Global settings manager instance
_settings_manager = SettingsManager()


def get_settings() -> Settings:
    """Get current FlowLens settings."""
    return _settings_manager.get_settings()


def update_settings(**kwargs) -> None:
    """Update FlowLens settings."""
    _settings_manager.update_settings(**kwargs)


def reset_settings() -> None:
    """Reset FlowLens settings to defaults."""
    _settings_manager.reset_settings()


def get_config_path() -> Path:
    """Get configuration file path."""
    return _settings_manager.get_config_path()


# Environment variable overrides
def apply_env_overrides(settings: Settings) -> None:
    """Apply environment variable overrides to settings."""
    # Performance settings
    if 'FLOWLENS_MAX_LATENCY_MS' in os.environ:
        try:
            settings.performance.max_latency_ms = float(os.environ['FLOWLENS_MAX_LATENCY_MS'])
        except ValueError:
            pass
    
    if 'FLOWLENS_MAX_FILE_SIZE_MB' in os.environ:
        try:
            settings.performance.max_file_size_mb = float(os.environ['FLOWLENS_MAX_FILE_SIZE_MB'])
        except ValueError:
            pass
    
    if 'FLOWLENS_ENABLE_CACHING' in os.environ:
        settings.performance.enable_caching = os.environ['FLOWLENS_ENABLE_CACHING'].lower() in ('true', '1', 'yes')
    
    # Visualization settings
    if 'FLOWLENS_THEME' in os.environ:
        settings.visualization.theme = os.environ['FLOWLENS_THEME']
    
    if 'FLOWLENS_DPI' in os.environ:
        try:
            settings.visualization.dpi = int(os.environ['FLOWLENS_DPI'])
        except ValueError:
            pass
    
    # Execution settings
    if 'FLOWLENS_MAX_STEPS' in os.environ:
        try:
            settings.execution.max_steps = int(os.environ['FLOWLENS_MAX_STEPS'])
        except ValueError:
            pass
    
    if 'FLOWLENS_TRACK_VARIABLES' in os.environ:
        settings.execution.track_variables = os.environ['FLOWLENS_TRACK_VARIABLES'].lower() in ('true', '1', 'yes')


# Apply environment overrides on import
apply_env_overrides(get_settings())
