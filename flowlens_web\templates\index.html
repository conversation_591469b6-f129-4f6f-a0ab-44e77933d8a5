<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlowLens - Interactive Python Code Visualization</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-project-diagram me-2"></i>
                FlowLens
            </a>
            <div class="navbar-nav ms-auto">
                <!-- Theme Toggle -->
                <div class="form-check form-switch me-3 d-flex align-items-center">
                    <input class="form-check-input" type="checkbox" id="themeToggle" role="switch">
                    <label class="form-check-label text-light ms-2" for="themeToggle">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </label>
                </div>

                <button class="btn btn-outline-light btn-sm" id="tierInfoBtn">
                    <i class="fas fa-crown me-1"></i>
                    <span id="tierStatus">Free Tier</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Left Panel - Code Input -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-code me-2"></i>
                            Python Code Input
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- File Upload -->
                        <div class="mb-3">
                            <label for="fileInput" class="form-label">Upload Python File</label>
                            <input type="file" class="form-control" id="fileInput" accept=".py">
                        </div>
                        
                        <!-- Code Editor -->
                        <div class="mb-3">
                            <label for="codeEditor" class="form-label">Or Paste Code Here</label>
                            <textarea id="codeEditor" class="form-control" rows="20" placeholder="# Paste your Python code here..."></textarea>
                        </div>
                        
                        <!-- Analysis Options -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="functionSelect" class="form-label">Function Filter (Optional)</label>
                                <select class="form-select" id="functionSelect">
                                    <option value="">Analyze entire file</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="visualizationType" class="form-label">Visualization Type</label>
                                <select class="form-select" id="visualizationType">
                                    <option value="analyze">Code Analysis</option>
                                    <option value="graph">Control Flow Graph</option>
                                    <option value="mermaid">Mermaid Diagram</option>
                                    <option value="heatmap">Variable Heatmap</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Visualization Options -->
                        <div class="row mb-3" id="visualizationOptions" style="display: none;">
                            <div class="col-md-4">
                                <label for="formatSelect" class="form-label">Format</label>
                                <select class="form-select" id="formatSelect">
                                    <option value="png">PNG Image</option>
                                    <option value="svg">SVG Vector</option>
                                    <option value="mmd">Mermaid Text</option>
                                    <option value="html">Interactive HTML</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="themeSelect" class="form-label">Theme</label>
                                <select class="form-select" id="themeSelect">
                                    <option value="default">Default</option>
                                    <option value="dark">Dark</option>
                                    <option value="forest">Forest</option>
                                    <option value="base">Base</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="directionSelect" class="form-label">Direction</label>
                                <select class="form-select" id="directionSelect">
                                    <option value="TD">Top-Down</option>
                                    <option value="LR">Left-Right</option>
                                    <option value="BT">Bottom-Top</option>
                                    <option value="RL">Right-Left</option>
                                </select>
                            </div>
                        </div>

                        <!-- Mermaid Preview -->
                        <div class="mb-3" id="mermaidPreviewContainer" style="display: none;">
                            <label class="form-label">Live Preview</label>
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <small class="text-muted">Mermaid Diagram Preview</small>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary" id="previewZoomOut">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="previewZoomReset">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="previewZoomIn">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body p-2">
                                    <div id="mermaidPreview" class="text-center" style="min-height: 200px; overflow: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; background: white;">
                                        <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                                            <i class="fas fa-project-diagram fa-2x me-2"></i>
                                            <span>Preview will appear here</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button class="btn btn-secondary" id="clearBtn">
                                <i class="fas fa-trash me-1"></i>
                                Clear
                            </button>
                            <button class="btn btn-primary" id="analyzeBtn">
                                <i class="fas fa-play me-1"></i>
                                Analyze Code
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel - Results -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Analysis Results
                        </h5>
                        <div class="btn-group" role="group" id="resultActions" style="display: none;">
                            <button class="btn btn-outline-primary btn-sm" id="downloadBtn">
                                <i class="fas fa-download me-1"></i>
                                Download
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" id="shareBtn">
                                <i class="fas fa-share me-1"></i>
                                Share
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Loading State -->
                        <div id="loadingState" class="text-center py-5" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-3">Analyzing your code...</p>
                        </div>
                        
                        <!-- Empty State -->
                        <div id="emptyState" class="text-center py-5 text-muted">
                            <i class="fas fa-code fa-3x mb-3"></i>
                            <h5>Ready to Analyze</h5>
                            <p>Upload a Python file or paste code to get started</p>
                        </div>
                        
                        <!-- Error State -->
                        <div id="errorState" class="alert alert-danger" style="display: none;">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Analysis Error</h6>
                            <p id="errorMessage"></p>
                            <div id="upgradePrompt" style="display: none;">
                                <hr>
                                <p class="mb-2"><strong>Upgrade Required:</strong></p>
                                <p id="upgradeMessage"></p>
                                <button class="btn btn-warning btn-sm" id="upgradeBtn">
                                    <i class="fas fa-crown me-1"></i>
                                    Upgrade Now
                                </button>
                            </div>
                        </div>
                        
                        <!-- Results Content -->
                        <div id="resultsContent" style="display: none;">
                            <!-- Analysis Results -->
                            <div id="analysisResults" style="display: none;">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h3 class="text-primary" id="totalLines">0</h3>
                                                <small class="text-muted">Total Lines</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h3 class="text-success" id="complexityScore">0</h3>
                                                <small class="text-muted">Complexity Score</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Functions Table -->
                                <div class="mb-4">
                                    <h6><i class="fas fa-function me-2"></i>Functions Found</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm" id="functionsTable">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Line</th>
                                                    <th>Args</th>
                                                    <th>Complexity</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <!-- Classes Table -->
                                <div class="mb-4">
                                    <h6><i class="fas fa-cube me-2"></i>Classes Found</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm" id="classesTable">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Line</th>
                                                    <th>Methods</th>
                                                    <th>Bases</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Visualization Results -->
                            <div id="visualizationResults" style="display: none;">
                                <!-- Image Display -->
                                <div id="imageDisplay" style="display: none;" class="text-center">
                                    <img id="resultImage" class="img-fluid border rounded" alt="Visualization Result">
                                </div>
                                
                                <!-- Mermaid Display -->
                                <div id="mermaidDisplay" style="display: none;">
                                    <div id="mermaidDiagram"></div>
                                </div>
                                
                                <!-- Text Display -->
                                <div id="textDisplay" style="display: none;">
                                    <pre><code id="textContent"></code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tier Info Modal -->
    <div class="modal fade" id="tierModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-crown me-2"></i>
                        Tier Information
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="tierInfo">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-warning" id="upgradeModalBtn">
                        <i class="fas fa-crown me-1"></i>
                        Upgrade Plan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
