"""
FlowLens AST Parser

This module provides comprehensive Python AST parsing and analysis capabilities.
It extracts code structure, identifies execution paths, and prepares data for
visualization and tracking.

Key Features:
- Parse Python files and code strings
- Extract function definitions, classes, and control structures
- Identify execution paths and decision points
- Support for Python 3.8+ syntax features
- Performance optimized for <200ms latency
"""

import ast
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union, Any
from dataclasses import dataclass, field
from collections import defaultdict

# Performance monitoring
from ..utils.performance import PerformanceMonitor

# Analytics and licensing
from .analytics import track_feature_usage, track_performance, track_error, track_operation
from .licensing import get_license_manager, check_feature_access


@dataclass
class ParseResult:
    """Container for AST parsing results."""
    
    # Basic information
    filepath: Optional[str] = None
    source_code: str = ""
    ast_tree: Optional[ast.AST] = None
    
    # Extracted structures
    functions: List[Dict[str, Any]] = field(default_factory=list)
    classes: List[Dict[str, Any]] = field(default_factory=list)
    imports: List[Dict[str, Any]] = field(default_factory=list)
    variables: Set[str] = field(default_factory=set)
    
    # Control flow information
    decision_points: List[Dict[str, Any]] = field(default_factory=list)
    loops: List[Dict[str, Any]] = field(default_factory=list)
    exception_handlers: List[Dict[str, Any]] = field(default_factory=list)
    
    # Execution paths
    execution_paths: List[List[int]] = field(default_factory=list)
    line_mapping: Dict[int, ast.AST] = field(default_factory=dict)
    
    # Metadata
    parse_time: float = 0.0
    complexity_score: int = 0
    total_lines: int = 0
    
    @property
    def summary(self) -> Dict[str, Any]:
        """Get a summary of parsing results."""
        return {
            "filepath": self.filepath,
            "total_lines": self.total_lines,
            "functions": len(self.functions),
            "classes": len(self.classes),
            "decision_points": len(self.decision_points),
            "loops": len(self.loops),
            "complexity_score": self.complexity_score,
            "parse_time_ms": round(self.parse_time * 1000, 2),
        }


class ASTParser:
    """
    Advanced AST parser for Python code analysis.
    
    This parser extracts comprehensive information about Python code structure
    and prepares it for visualization and execution tracking.
    """
    
    def __init__(self, enable_performance_monitoring: bool = True):
        self.performance_monitor = PerformanceMonitor() if enable_performance_monitoring else None
        self._node_visitors = self._setup_node_visitors()
    
    def parse_file(self, filepath: Union[str, Path]) -> ParseResult:
        """
        Parse a Python file and extract comprehensive information.
        
        Args:
            filepath: Path to the Python file
            
        Returns:
            ParseResult containing all extracted information
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            SyntaxError: If the file contains invalid Python syntax
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"File not found: {filepath}")
        
        if not filepath.suffix == '.py':
            raise ValueError(f"File must be a Python file (.py): {filepath}")
        
        # Read source code
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                source_code = f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(filepath, 'r', encoding='latin-1') as f:
                source_code = f.read()
        
        return self.parse_code(source_code, str(filepath))
    
    def parse_code(self, source_code: str, filepath: Optional[str] = None) -> ParseResult:
        """
        Parse Python source code and extract comprehensive information.

        Args:
            source_code: Python source code as string
            filepath: Optional filepath for reference

        Returns:
            ParseResult containing all extracted information

        Raises:
            SyntaxError: If the code contains invalid Python syntax
        """
        start_time = time.time()
        total_lines = len(source_code.splitlines())

        # Check licensing limits
        can_analyze, upgrade_prompt = check_feature_access('file_analysis', {'file_lines': total_lines})
        if not can_analyze:
            track_feature_usage('file_analysis_blocked', {
                'file_lines': total_lines,
                'reason': 'tier_limit'
            })
            raise ValueError(f"File analysis blocked: {upgrade_prompt.message}")

        # Track analytics
        track_feature_usage('file_analysis_started', {
            'file_lines': total_lines,
            'has_filepath': filepath is not None
        })

        if self.performance_monitor:
            self.performance_monitor.start_operation("ast_parsing")

        try:
            with track_operation("ast_parsing", {'file_lines': total_lines}):
                # Parse AST
                ast_tree = ast.parse(source_code, filename=filepath or "<string>")

                # Create result container
                result = ParseResult(
                    filepath=filepath,
                    source_code=source_code,
                    ast_tree=ast_tree,
                    total_lines=total_lines
                )
            
            # Extract information using visitor pattern
            self._extract_information(ast_tree, result)
            
            # Calculate complexity score
            result.complexity_score = self._calculate_complexity(result)
            
            # Record parse time
            result.parse_time = time.time() - start_time

            if self.performance_monitor:
                self.performance_monitor.end_operation("ast_parsing", result.parse_time)

            # Record successful analysis
            license_manager = get_license_manager()
            license_manager.record_file_analysis(total_lines)

            # Track completion analytics
            track_feature_usage('file_analysis_completed', {
                'file_lines': total_lines,
                'functions_found': len(result.functions),
                'classes_found': len(result.classes),
                'complexity_score': result.complexity_score,
                'parse_time_ms': result.parse_time * 1000
            })

            return result

        except SyntaxError as e:
            if self.performance_monitor:
                self.performance_monitor.end_operation("ast_parsing", time.time() - start_time, error=str(e))

            # Track error analytics
            track_error('SyntaxError', str(e), {
                'file_lines': total_lines,
                'has_filepath': filepath is not None
            })
            raise
    
    def _setup_node_visitors(self) -> Dict[type, callable]:
        """Setup mapping of AST node types to visitor methods."""
        return {
            ast.FunctionDef: self._visit_function,
            ast.AsyncFunctionDef: self._visit_async_function,
            ast.ClassDef: self._visit_class,
            ast.Import: self._visit_import,
            ast.ImportFrom: self._visit_import_from,
            ast.If: self._visit_if,
            ast.For: self._visit_for,
            ast.While: self._visit_while,
            ast.Try: self._visit_try,
            ast.With: self._visit_with,
            ast.Assign: self._visit_assign,
            ast.AnnAssign: self._visit_ann_assign,
            ast.AugAssign: self._visit_aug_assign,
        }
    
    def _extract_information(self, ast_tree: ast.AST, result: ParseResult) -> None:
        """Extract all information from AST tree."""
        # Build line mapping
        for node in ast.walk(ast_tree):
            if hasattr(node, 'lineno'):
                result.line_mapping[node.lineno] = node
        
        # Visit all nodes
        for node in ast.walk(ast_tree):
            visitor = self._node_visitors.get(type(node))
            if visitor:
                visitor(node, result)
    
    def _visit_function(self, node: ast.FunctionDef, result: ParseResult) -> None:
        """Extract function information."""
        func_info = {
            "name": node.name,
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "args": [arg.arg for arg in node.args.args],
            "defaults": len(node.args.defaults),
            "decorators": [self._get_decorator_name(dec) for dec in node.decorator_list],
            "is_async": False,
            "docstring": ast.get_docstring(node),
            "complexity": self._calculate_function_complexity(node),
        }
        result.functions.append(func_info)
    
    def _visit_async_function(self, node: ast.AsyncFunctionDef, result: ParseResult) -> None:
        """Extract async function information."""
        func_info = {
            "name": node.name,
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "args": [arg.arg for arg in node.args.args],
            "defaults": len(node.args.defaults),
            "decorators": [self._get_decorator_name(dec) for dec in node.decorator_list],
            "is_async": True,
            "docstring": ast.get_docstring(node),
            "complexity": self._calculate_function_complexity(node),
        }
        result.functions.append(func_info)
    
    def _visit_class(self, node: ast.ClassDef, result: ParseResult) -> None:
        """Extract class information."""
        class_info = {
            "name": node.name,
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "bases": [self._get_name(base) for base in node.bases],
            "decorators": [self._get_decorator_name(dec) for dec in node.decorator_list],
            "methods": [],
            "docstring": ast.get_docstring(node),
        }
        
        # Extract methods
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                class_info["methods"].append(item.name)
        
        result.classes.append(class_info)
    
    def _visit_import(self, node: ast.Import, result: ParseResult) -> None:
        """Extract import information."""
        for alias in node.names:
            import_info = {
                "type": "import",
                "module": alias.name,
                "alias": alias.asname,
                "lineno": node.lineno,
            }
            result.imports.append(import_info)
    
    def _visit_import_from(self, node: ast.ImportFrom, result: ParseResult) -> None:
        """Extract from-import information."""
        for alias in node.names:
            import_info = {
                "type": "from_import",
                "module": node.module,
                "name": alias.name,
                "alias": alias.asname,
                "level": node.level,
                "lineno": node.lineno,
            }
            result.imports.append(import_info)
    
    def _visit_if(self, node: ast.If, result: ParseResult) -> None:
        """Extract if statement information."""
        decision_info = {
            "type": "if",
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "condition": ast.unparse(node.test) if sys.version_info >= (3, 9) else "condition",
            "has_else": bool(node.orelse),
            "elif_count": self._count_elif(node),
        }
        result.decision_points.append(decision_info)
    
    def _visit_for(self, node: ast.For, result: ParseResult) -> None:
        """Extract for loop information."""
        loop_info = {
            "type": "for",
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "target": ast.unparse(node.target) if sys.version_info >= (3, 9) else "target",
            "iter": ast.unparse(node.iter) if sys.version_info >= (3, 9) else "iterable",
            "has_else": bool(node.orelse),
        }
        result.loops.append(loop_info)
    
    def _visit_while(self, node: ast.While, result: ParseResult) -> None:
        """Extract while loop information."""
        loop_info = {
            "type": "while",
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "condition": ast.unparse(node.test) if sys.version_info >= (3, 9) else "condition",
            "has_else": bool(node.orelse),
        }
        result.loops.append(loop_info)
    
    def _visit_try(self, node: ast.Try, result: ParseResult) -> None:
        """Extract try-except information."""
        handler_info = {
            "type": "try",
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "handlers": len(node.handlers),
            "has_else": bool(node.orelse),
            "has_finally": bool(node.finalbody),
            "exception_types": [self._get_exception_type(handler) for handler in node.handlers],
        }
        result.exception_handlers.append(handler_info)
    
    def _visit_with(self, node: ast.With, result: ParseResult) -> None:
        """Extract with statement information."""
        # With statements are also decision points for resource management
        decision_info = {
            "type": "with",
            "lineno": node.lineno,
            "end_lineno": getattr(node, 'end_lineno', None),
            "items": len(node.items),
        }
        result.decision_points.append(decision_info)
    
    def _visit_assign(self, node: ast.Assign, result: ParseResult) -> None:
        """Extract variable assignments."""
        for target in node.targets:
            if isinstance(target, ast.Name):
                result.variables.add(target.id)
    
    def _visit_ann_assign(self, node: ast.AnnAssign, result: ParseResult) -> None:
        """Extract annotated assignments."""
        if isinstance(node.target, ast.Name):
            result.variables.add(node.target.id)
    
    def _visit_aug_assign(self, node: ast.AugAssign, result: ParseResult) -> None:
        """Extract augmented assignments."""
        if isinstance(node.target, ast.Name):
            result.variables.add(node.target.id)
    
    # Helper methods
    def _get_decorator_name(self, decorator: ast.expr) -> str:
        """Get decorator name as string."""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return f"{self._get_name(decorator.value)}.{decorator.attr}"
        else:
            return "decorator"
    
    def _get_name(self, node: ast.expr) -> str:
        """Get name from AST node."""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_name(node.value)}.{node.attr}"
        else:
            return "unknown"
    
    def _count_elif(self, node: ast.If) -> int:
        """Count elif statements in if chain."""
        count = 0
        current = node
        while current.orelse and len(current.orelse) == 1 and isinstance(current.orelse[0], ast.If):
            count += 1
            current = current.orelse[0]
        return count
    
    def _get_exception_type(self, handler: ast.ExceptHandler) -> str:
        """Get exception type name."""
        if handler.type:
            return self._get_name(handler.type)
        return "Exception"
    
    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity of a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _calculate_complexity(self, result: ParseResult) -> int:
        """Calculate overall complexity score."""
        complexity = 0
        complexity += len(result.decision_points) * 2
        complexity += len(result.loops) * 3
        complexity += len(result.exception_handlers) * 2
        complexity += sum(func.get("complexity", 1) for func in result.functions)
        return complexity
