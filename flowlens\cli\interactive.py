"""
FlowLens Interactive Session

This module provides an interactive REPL session for FlowLens,
allowing users to analyze code interactively with command history
and session management.

Features:
- Interactive command processing
- Command history and completion
- Session state management
- Real-time code analysis
"""

import cmd
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.syntax import Syntax

from ..core import <PERSON>TParser, CFGGenerator, ExecutionTracker, VariableMonitor
from ..utils.validators import CodeValidator
from ..utils.file_handler import CodeLoader

console = Console()


class InteractiveSession(cmd.Cmd):
    """
    Interactive FlowLens session with command processing.
    
    Provides a REPL interface for analyzing Python code with
    persistent session state and command history.
    """
    
    intro = """
[blue]FlowLens Interactive Session[/blue]
Type 'help' or '?' to list commands.
Type 'exit' or 'quit' to exit.
    """.strip()
    
    prompt = "[flowlens] "
    
    def __init__(self, history_size: int = 1000):
        super().__init__()
        self.history_size = history_size
        self.console = console
        
        # Initialize components
        self.parser = ASTParser()
        self.cfg_generator = CFGGenerator()
        self.tracker = ExecutionTracker()
        self.monitor = VariableMonitor()
        self.code_validator = CodeValidator()
        self.code_loader = CodeLoader()
        
        # Session state
        self.current_file: Optional[Path] = None
        self.current_code: Optional[str] = None
        self.current_parse_result = None
        self.current_cfg = None
        self.session_history: List[Dict[str, Any]] = []
        
        # Command aliases
        self.aliases = {
            'q': 'quit',
            'h': 'help',
            'l': 'load',
            'a': 'analyze',
            's': 'step',
            'g': 'graph',
            'v': 'variables',
        }
    
    def run(self):
        """Run the interactive session."""
        self.console.print(self.intro)
        try:
            self.cmdloop()
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Session interrupted[/yellow]")
    
    def default(self, line):
        """Handle unknown commands and aliases."""
        # Check for aliases
        parts = line.split()
        if parts and parts[0] in self.aliases:
            new_line = line.replace(parts[0], self.aliases[parts[0]], 1)
            return self.onecmd(new_line)
        
        # Try to execute as Python code
        if line.strip():
            self.do_exec(line)
    
    def do_load(self, arg):
        """Load a Python file for analysis.
        Usage: load <filepath>
        """
        if not arg:
            self.console.print("[red]Error:[/red] Please specify a file path")
            return
        
        try:
            filepath = Path(arg.strip())
            if not filepath.exists():
                self.console.print(f"[red]Error:[/red] File not found: {filepath}")
                return
            
            # Load file
            content, file_info = self.code_loader.load_python_file(filepath)
            
            self.current_file = filepath
            self.current_code = content
            self.current_parse_result = None
            self.current_cfg = None
            
            self.console.print(f"[green]Loaded:[/green] {filepath}")
            self.console.print(f"Lines: {file_info.line_count}, Size: {file_info.size_kb:.1f}KB")
            
        except Exception as e:
            self.console.print(f"[red]Error loading file:[/red] {e}")
    
    def do_analyze(self, arg):
        """Analyze the currently loaded code.
        Usage: analyze [function_name]
        """
        if not self.current_code:
            self.console.print("[red]Error:[/red] No code loaded. Use 'load <file>' first.")
            return
        
        try:
            function_name = arg.strip() if arg.strip() else None
            
            # Parse code
            self.console.print("Parsing code...")
            self.current_parse_result = self.parser.parse_code(
                self.current_code, str(self.current_file) if self.current_file else None
            )
            
            # Display results
            self._display_parse_results(function_name)
            
        except Exception as e:
            self.console.print(f"[red]Analysis failed:[/red] {e}")
    
    def do_step(self, arg):
        """Start step-through execution.
        Usage: step [function_name] [max_steps]
        """
        if not self.current_code:
            self.console.print("[red]Error:[/red] No code loaded. Use 'load <file>' first.")
            return
        
        try:
            parts = arg.split() if arg else []
            function_name = parts[0] if len(parts) > 0 else None
            max_steps = int(parts[1]) if len(parts) > 1 else 100
            
            # Generate CFG if needed
            if not self.current_cfg:
                if not self.current_parse_result:
                    self.current_parse_result = self.parser.parse_code(self.current_code)
                self.current_cfg = self.cfg_generator.generate_cfg(self.current_parse_result, function_name)
            
            # Start execution
            execution_id = self.tracker.start_tracking(self.current_code, self.current_cfg)
            
            self.console.print(f"[blue]Starting step execution (max {max_steps} steps)[/blue]")
            self.console.print("Commands: [Enter] = next step, 'q' = quit, 'v' = show variables")
            
            step_count = 0
            while step_count < max_steps:
                step = self.tracker.step_execute(execution_id)
                if step is None:
                    self.console.print("[green]Execution completed[/green]")
                    break
                
                step_count += 1
                self._display_step(step, step_count)
                
                # Get user input
                user_input = input(">>> ").strip().lower()
                
                if user_input == 'q':
                    break
                elif user_input == 'v':
                    self._display_step_variables(step)
            
            # Finalize
            result = self.tracker.stop_execution(execution_id)
            if result:
                self.console.print(f"[green]Completed in {result.duration_ms:.2f}ms[/green]")
            
        except Exception as e:
            self.console.print(f"[red]Step execution failed:[/red] {e}")
    
    def do_graph(self, arg):
        """Generate control flow graph.
        Usage: graph [function_name]
        """
        if not self.current_code:
            self.console.print("[red]Error:[/red] No code loaded. Use 'load <file>' first.")
            return
        
        try:
            function_name = arg.strip() if arg.strip() else None
            
            # Generate CFG
            if not self.current_parse_result:
                self.current_parse_result = self.parser.parse_code(self.current_code)
            
            self.current_cfg = self.cfg_generator.generate_cfg(self.current_parse_result, function_name)
            
            # Display CFG summary
            self._display_cfg_summary()
            
        except Exception as e:
            self.console.print(f"[red]Graph generation failed:[/red] {e}")
    
    def do_variables(self, arg):
        """Show variable analysis.
        Usage: variables
        """
        if not self.current_code:
            self.console.print("[red]Error:[/red] No code loaded. Use 'load <file>' first.")
            return
        
        try:
            # Execute and analyze variables
            if not self.current_cfg:
                if not self.current_parse_result:
                    self.current_parse_result = self.parser.parse_code(self.current_code)
                self.current_cfg = self.cfg_generator.generate_cfg(self.current_parse_result)
            
            execution_id = self.tracker.start_tracking(self.current_code, self.current_cfg)
            result = self.tracker.run_to_completion(execution_id)
            
            self.monitor.start_monitoring()
            analysis = self.monitor.process_execution_result(result)
            
            self._display_variable_analysis(analysis)
            
        except Exception as e:
            self.console.print(f"[red]Variable analysis failed:[/red] {e}")
    
    def do_exec(self, arg):
        """Execute Python code directly.
        Usage: exec <python_code>
        """
        if not arg.strip():
            self.console.print("[red]Error:[/red] Please provide Python code to execute")
            return
        
        try:
            # Validate code
            validation = self.code_validator.validate_code_string(arg)
            if not validation.is_valid:
                self.console.print(f"[red]Invalid code:[/red] {'; '.join(validation.errors)}")
                return
            
            # Execute code
            exec(arg)
            self.console.print("[green]Code executed successfully[/green]")
            
        except Exception as e:
            self.console.print(f"[red]Execution error:[/red] {e}")
    
    def do_history(self, arg):
        """Show command history.
        Usage: history [count]
        """
        count = 10
        if arg.strip():
            try:
                count = int(arg.strip())
            except ValueError:
                self.console.print("[red]Error:[/red] Invalid count")
                return
        
        if not self.session_history:
            self.console.print("[yellow]No command history[/yellow]")
            return
        
        table = Table(title="Command History")
        table.add_column("#", style="cyan")
        table.add_column("Command", style="green")
        table.add_column("Time", style="yellow")
        
        for i, entry in enumerate(self.session_history[-count:], 1):
            table.add_row(str(i), entry['command'], entry['timestamp'])
        
        self.console.print(table)
    
    def do_status(self, arg):
        """Show current session status.
        Usage: status
        """
        table = Table(title="Session Status")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Current File", str(self.current_file) if self.current_file else "None")
        table.add_row("Code Loaded", "Yes" if self.current_code else "No")
        table.add_row("Parsed", "Yes" if self.current_parse_result else "No")
        table.add_row("CFG Generated", "Yes" if self.current_cfg else "No")
        table.add_row("Commands Run", str(len(self.session_history)))
        
        self.console.print(table)
    
    def do_clear(self, arg):
        """Clear the current session.
        Usage: clear
        """
        self.current_file = None
        self.current_code = None
        self.current_parse_result = None
        self.current_cfg = None
        self.console.print("[green]Session cleared[/green]")
    
    def do_quit(self, arg):
        """Exit the interactive session.
        Usage: quit
        """
        self.console.print("[blue]Goodbye![/blue]")
        return True
    
    def do_exit(self, arg):
        """Exit the interactive session.
        Usage: exit
        """
        return self.do_quit(arg)
    
    # Helper methods for display
    
    def _display_parse_results(self, function_name: Optional[str]):
        """Display parsing results."""
        if not self.current_parse_result:
            return
        
        summary = self.current_parse_result.summary
        
        table = Table(title="Analysis Results")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        for key, value in summary.items():
            table.add_row(key.replace('_', ' ').title(), str(value))
        
        self.console.print(table)
        
        if function_name:
            # Show specific function info
            func_info = next((f for f in self.current_parse_result.functions if f['name'] == function_name), None)
            if func_info:
                self.console.print(f"[blue]Function '{function_name}' found at line {func_info['lineno']}[/blue]")
            else:
                self.console.print(f"[yellow]Function '{function_name}' not found[/yellow]")
    
    def _display_step(self, step, step_count: int):
        """Display execution step."""
        content = f"Step {step_count}\nLine {step.line_number}: {step.statement}"
        panel = Panel(content, title=f"Execution Step {step_count}")
        self.console.print(panel)
    
    def _display_step_variables(self, step):
        """Display step variables."""
        if not step.all_variables:
            self.console.print("[yellow]No variables[/yellow]")
            return
        
        table = Table(title="Variables")
        table.add_column("Name", style="cyan")
        table.add_column("Value", style="green")
        table.add_column("Type", style="yellow")
        
        for name, value in step.all_variables.items():
            if not name.startswith('__'):
                table.add_row(name, str(value)[:50], type(value).__name__)
        
        self.console.print(table)
    
    def _display_cfg_summary(self):
        """Display CFG summary."""
        if not self.current_cfg:
            return
        
        metrics = self.current_cfg.get_complexity_metrics()
        
        table = Table(title="Control Flow Graph")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        for key, value in metrics.items():
            table.add_row(key.replace('_', ' ').title(), str(value))
        
        self.console.print(table)
    
    def _display_variable_analysis(self, analysis):
        """Display variable analysis."""
        table = Table(title="Variable Analysis")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Variables", str(analysis['total_variables']))
        table.add_row("Total Changes", str(analysis['total_changes']))
        
        self.console.print(table)
        
        # Show most active variables
        if analysis['most_active_variables']:
            var_table = Table(title="Most Active Variables")
            var_table.add_column("Name", style="cyan")
            var_table.add_column("Activity Score", style="green")
            
            for var in analysis['most_active_variables'][:5]:
                var_table.add_row(var['name'], str(var['activity_score']))
            
            self.console.print(var_table)
