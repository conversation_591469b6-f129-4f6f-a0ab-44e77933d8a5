"""
FlowLens Themes Configuration

This module provides theme management for FlowLens visualizations,
including color schemes, styling options, and theme customization.
"""

from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class Theme:
    """Theme configuration for FlowLens visualizations."""
    
    name: str
    description: str
    
    # Node colors for different types
    node_colors: Dict[str, str]
    
    # Edge colors for different types
    edge_colors: Dict[str, str]
    
    # Background and text colors
    background_color: str
    text_color: str
    grid_color: str
    
    # Font settings
    font_family: str
    font_size: int
    
    # Style settings
    node_border_width: float
    edge_width: float
    alpha: float


# Default theme
DEFAULT_THEME = Theme(
    name="default",
    description="Default FlowLens theme with professional colors",
    node_colors={
        "entry": "#4CAF50",           # Green
        "exit": "#F44336",            # Red
        "statement": "#2196F3",       # Blue
        "condition": "#FF9800",       # Orange
        "loop_header": "#9C27B0",     # Purple
        "loop_body": "#E91E63",       # Pink
        "exception_handler": "#FF5722", # Deep Orange
        "function_call": "#00BCD4",   # Cyan
        "return": "#795548",          # Brown
    },
    edge_colors={
        "normal": "#000000",          # Black
        "true": "#4CAF50",            # Green
        "false": "#F44336",           # Red
        "exception": "#FF5722",       # Deep Orange
        "break": "#FF9800",           # Orange
        "continue": "#2196F3",        # Blue
    },
    background_color="#FFFFFF",       # White
    text_color="#000000",             # Black
    grid_color="#CCCCCC",             # Light Gray
    font_family="Arial",
    font_size=10,
    node_border_width=1.5,
    edge_width=1.5,
    alpha=0.8
)

# Dark theme
DARK_THEME = Theme(
    name="dark",
    description="Dark theme optimized for low-light environments",
    node_colors={
        "entry": "#66BB6A",           # Light Green
        "exit": "#EF5350",            # Light Red
        "statement": "#42A5F5",       # Light Blue
        "condition": "#FFA726",       # Light Orange
        "loop_header": "#AB47BC",     # Light Purple
        "loop_body": "#EC407A",       # Light Pink
        "exception_handler": "#FF7043", # Light Deep Orange
        "function_call": "#26C6DA",   # Light Cyan
        "return": "#8D6E63",          # Light Brown
    },
    edge_colors={
        "normal": "#FFFFFF",          # White
        "true": "#66BB6A",            # Light Green
        "false": "#EF5350",           # Light Red
        "exception": "#FF7043",       # Light Deep Orange
        "break": "#FFA726",           # Light Orange
        "continue": "#42A5F5",        # Light Blue
    },
    background_color="#121212",       # Dark Gray
    text_color="#FFFFFF",             # White
    grid_color="#444444",             # Medium Gray
    font_family="Arial",
    font_size=10,
    node_border_width=1.5,
    edge_width=1.5,
    alpha=0.9
)

# Light theme
LIGHT_THEME = Theme(
    name="light",
    description="Light theme with soft, pastel colors",
    node_colors={
        "entry": "#A5D6A7",           # Light Green
        "exit": "#FFCDD2",            # Light Red
        "statement": "#BBDEFB",       # Light Blue
        "condition": "#FFE0B2",       # Light Orange
        "loop_header": "#E1BEE7",     # Light Purple
        "loop_body": "#F8BBD9",       # Light Pink
        "exception_handler": "#FFCCBC", # Light Deep Orange
        "function_call": "#B2EBF2",   # Light Cyan
        "return": "#D7CCC8",          # Light Brown
    },
    edge_colors={
        "normal": "#666666",          # Dark Gray
        "true": "#4CAF50",            # Green
        "false": "#F44336",           # Red
        "exception": "#FF5722",       # Deep Orange
        "break": "#FF9800",           # Orange
        "continue": "#2196F3",        # Blue
    },
    background_color="#FAFAFA",       # Very Light Gray
    text_color="#333333",             # Dark Gray
    grid_color="#E0E0E0",             # Light Gray
    font_family="Arial",
    font_size=10,
    node_border_width=1.0,
    edge_width=1.0,
    alpha=0.7
)

# Colorful theme
COLORFUL_THEME = Theme(
    name="colorful",
    description="Vibrant, high-contrast theme for presentations",
    node_colors={
        "entry": "#00E676",           # Bright Green
        "exit": "#FF1744",            # Bright Red
        "statement": "#2979FF",       # Bright Blue
        "condition": "#FF6D00",       # Bright Orange
        "loop_header": "#D500F9",     # Bright Purple
        "loop_body": "#E91E63",       # Bright Pink
        "exception_handler": "#FF3D00", # Bright Deep Orange
        "function_call": "#00E5FF",   # Bright Cyan
        "return": "#6D4C41",          # Dark Brown
    },
    edge_colors={
        "normal": "#000000",          # Black
        "true": "#00E676",            # Bright Green
        "false": "#FF1744",           # Bright Red
        "exception": "#FF3D00",       # Bright Deep Orange
        "break": "#FF6D00",           # Bright Orange
        "continue": "#2979FF",        # Bright Blue
    },
    background_color="#FFFFFF",       # White
    text_color="#000000",             # Black
    grid_color="#BDBDBD",             # Gray
    font_family="Arial",
    font_size=11,
    node_border_width=2.0,
    edge_width=2.0,
    alpha=0.9
)

# Minimal theme
MINIMAL_THEME = Theme(
    name="minimal",
    description="Clean, minimal theme with reduced visual clutter",
    node_colors={
        "entry": "#757575",           # Gray
        "exit": "#757575",            # Gray
        "statement": "#9E9E9E",       # Light Gray
        "condition": "#616161",       # Dark Gray
        "loop_header": "#424242",     # Very Dark Gray
        "loop_body": "#757575",       # Gray
        "exception_handler": "#616161", # Dark Gray
        "function_call": "#9E9E9E",   # Light Gray
        "return": "#757575",          # Gray
    },
    edge_colors={
        "normal": "#424242",          # Very Dark Gray
        "true": "#424242",            # Very Dark Gray
        "false": "#424242",           # Very Dark Gray
        "exception": "#424242",       # Very Dark Gray
        "break": "#424242",           # Very Dark Gray
        "continue": "#424242",        # Very Dark Gray
    },
    background_color="#FFFFFF",       # White
    text_color="#212121",             # Very Dark Gray
    grid_color="#F5F5F5",             # Very Light Gray
    font_family="Arial",
    font_size=9,
    node_border_width=1.0,
    edge_width=1.0,
    alpha=0.6
)

# Theme registry
THEMES = {
    "default": DEFAULT_THEME,
    "dark": DARK_THEME,
    "light": LIGHT_THEME,
    "colorful": COLORFUL_THEME,
    "minimal": MINIMAL_THEME,
}


def get_theme(name: str) -> Theme:
    """Get a theme by name."""
    if name not in THEMES:
        raise ValueError(f"Unknown theme: {name}. Available themes: {list_themes()}")
    return THEMES[name]


def list_themes() -> List[str]:
    """Get list of available theme names."""
    return list(THEMES.keys())


def get_theme_info() -> Dict[str, str]:
    """Get information about all available themes."""
    return {name: theme.description for name, theme in THEMES.items()}


def register_theme(theme: Theme) -> None:
    """Register a custom theme."""
    THEMES[theme.name] = theme


def create_custom_theme(name: str, base_theme: str = "default", **overrides) -> Theme:
    """Create a custom theme based on an existing theme."""
    if base_theme not in THEMES:
        raise ValueError(f"Base theme '{base_theme}' not found")
    
    base = THEMES[base_theme]
    
    # Create new theme with overrides
    theme_data = {
        "name": name,
        "description": overrides.get("description", f"Custom theme based on {base_theme}"),
        "node_colors": overrides.get("node_colors", base.node_colors.copy()),
        "edge_colors": overrides.get("edge_colors", base.edge_colors.copy()),
        "background_color": overrides.get("background_color", base.background_color),
        "text_color": overrides.get("text_color", base.text_color),
        "grid_color": overrides.get("grid_color", base.grid_color),
        "font_family": overrides.get("font_family", base.font_family),
        "font_size": overrides.get("font_size", base.font_size),
        "node_border_width": overrides.get("node_border_width", base.node_border_width),
        "edge_width": overrides.get("edge_width", base.edge_width),
        "alpha": overrides.get("alpha", base.alpha),
    }
    
    return Theme(**theme_data)


def export_theme(theme: Theme) -> Dict[str, Any]:
    """Export theme to dictionary format."""
    return {
        "name": theme.name,
        "description": theme.description,
        "node_colors": theme.node_colors,
        "edge_colors": theme.edge_colors,
        "background_color": theme.background_color,
        "text_color": theme.text_color,
        "grid_color": theme.grid_color,
        "font_family": theme.font_family,
        "font_size": theme.font_size,
        "node_border_width": theme.node_border_width,
        "edge_width": theme.edge_width,
        "alpha": theme.alpha,
    }


def import_theme(theme_data: Dict[str, Any]) -> Theme:
    """Import theme from dictionary format."""
    return Theme(**theme_data)
