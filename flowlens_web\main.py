"""
FlowLens Web Application - Main FastAPI Application

This module provides the main FastAPI application with all endpoints
for the FlowLens web interface.
"""

import os
import tempfile
import uuid
from pathlib import Path
from typing import Optional, Dict, Any, List
import json
import base64
from io import BytesIO

from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import H<PERSON><PERSON><PERSON>ponse, JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# FlowLens imports
from flowlens.core import ASTParser, CFGGenerator, ExecutionTracker, VariableMonitor
from flowlens.core.architecture_analyzer import ArchitectureAnalyzer
from flowlens.visualization import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HeatmapRenderer, MermaidRenderer
from flowlens.core.licensing import get_license_manager, check_feature_access
from flowlens.core.analytics import track_feature_usage

# Initialize FastAPI app
app = FastAPI(
    title="FlowLens",
    description="Interactive Python Code Visualization & Analysis",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
# Get absolute paths to avoid working directory issues
template_dir = Path(__file__).parent / "templates"
static_dir = Path(__file__).parent / "static"

templates = Jinja2Templates(directory=str(template_dir))
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Initialize FlowLens components
ast_parser = ASTParser()
cfg_generator = CFGGenerator()
execution_tracker = ExecutionTracker()
variable_monitor = VariableMonitor()
architecture_analyzer = ArchitectureAnalyzer()

# Initialize renderers
flow_renderer = FlowDiagramRenderer()
heatmap_renderer = HeatmapRenderer()
mermaid_renderer = MermaidRenderer()

# Temporary file storage for user sessions
temp_files: Dict[str, Path] = {}


# Pydantic models for API requests/responses
class CodeAnalysisRequest(BaseModel):
    code: str
    filename: Optional[str] = "code.py"
    function_name: Optional[str] = None


class VisualizationRequest(BaseModel):
    code: str
    filename: Optional[str] = "code.py"
    function_name: Optional[str] = None
    visualization_type: str  # "graph", "mermaid", "heatmap"
    format: str = "png"
    theme: str = "default"
    direction: str = "TD"  # For Mermaid diagrams


class AnalysisResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    upgrade_prompt: Optional[str] = None


class VisualizationResponse(BaseModel):
    success: bool
    content: Optional[str] = None  # Base64 encoded image or Mermaid text
    content_type: str = "image/png"
    filename: str = "visualization.png"
    error: Optional[str] = None
    upgrade_prompt: Optional[str] = None


@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Serve the main web interface."""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "1.0.0"}


@app.post("/api/analyze", response_model=AnalysisResponse)
async def analyze_code(request: CodeAnalysisRequest):
    """Analyze Python code and return comprehensive results."""
    try:
        # Track API usage
        track_feature_usage('web_analysis_started', {
            'code_length': len(request.code),
            'has_function_filter': request.function_name is not None
        })
        
        # Parse the code
        parse_result = ast_parser.parse_code(request.code, request.filename)
        
        # Generate CFG
        cfg = cfg_generator.generate_cfg(parse_result, request.function_name)
        
        # Prepare response data
        response_data = {
            "file_info": {
                "filename": request.filename,
                "total_lines": parse_result.total_lines,
                "functions": len(parse_result.functions),
                "classes": len(parse_result.classes),
                "complexity_score": parse_result.complexity_score,
                "parse_time": parse_result.parse_time
            },
            "functions": [
                {
                    "name": func["name"],
                    "line": func["lineno"],
                    "args": len(func.get("args", [])),
                    "complexity": func.get("complexity", 1)
                }
                for func in parse_result.functions
            ],
            "classes": [
                {
                    "name": cls["name"],
                    "line": cls["lineno"],
                    "methods": len(cls.get("methods", [])),
                    "bases": cls.get("bases", [])
                }
                for cls in parse_result.classes
            ],
            "cfg_info": {
                "nodes": len(cfg.nodes),
                "edges": len(cfg.edges),
                "entry_node": cfg.entry_node,
                "exit_nodes": list(cfg.exit_nodes)
            }
        }
        
        track_feature_usage('web_analysis_completed', {
            'functions_found': len(parse_result.functions),
            'classes_found': len(parse_result.classes),
            'complexity_score': parse_result.complexity_score
        })
        
        return AnalysisResponse(success=True, data=response_data)
        
    except ValueError as e:
        # Handle licensing/tier limit errors
        if "tier" in str(e).lower():
            return AnalysisResponse(
                success=False,
                error="Analysis limit reached",
                upgrade_prompt=str(e)
            )
        else:
            return AnalysisResponse(success=False, error=str(e))
    
    except Exception as e:
        return AnalysisResponse(success=False, error=f"Analysis failed: {str(e)}")


@app.post("/api/visualize", response_model=VisualizationResponse)
async def create_visualization(request: VisualizationRequest):
    """Generate visualization from Python code."""
    try:
        # Track visualization request
        track_feature_usage('web_visualization_started', {
            'visualization_type': request.visualization_type,
            'format': request.format,
            'code_length': len(request.code)
        })
        
        # Parse the code
        parse_result = ast_parser.parse_code(request.code, request.filename)
        
        if request.visualization_type == "mermaid":
            return await _generate_mermaid_visualization(request, parse_result)
        elif request.visualization_type == "graph":
            return await _generate_graph_visualization(request, parse_result)
        elif request.visualization_type == "heatmap":
            return await _generate_heatmap_visualization(request, parse_result)
        else:
            return VisualizationResponse(
                success=False,
                error=f"Unsupported visualization type: {request.visualization_type}"
            )
            
    except ValueError as e:
        # Handle licensing/tier limit errors
        if "tier" in str(e).lower() or "not available" in str(e).lower():
            return VisualizationResponse(
                success=False,
                error="Feature not available in your tier",
                upgrade_prompt=str(e)
            )
        else:
            return VisualizationResponse(success=False, error=str(e))
    
    except Exception as e:
        return VisualizationResponse(
            success=False,
            error=f"Visualization failed: {str(e)}"
        )


async def _generate_mermaid_visualization(request: VisualizationRequest, parse_result) -> VisualizationResponse:
    """Generate Mermaid diagram visualization."""
    # Generate CFG
    cfg = cfg_generator.generate_cfg(parse_result, request.function_name)
    
    # Set renderer options
    mermaid_renderer.theme = request.theme
    mermaid_renderer.direction = request.direction
    
    # Generate Mermaid content
    title = f"Control Flow: {request.filename}"
    if request.function_name:
        title += f" - {request.function_name}()"

    mermaid_content = mermaid_renderer.render_flowchart(cfg, title, request.filename)
    
    if request.format == "mmd":
        return VisualizationResponse(
            success=True,
            content=mermaid_content,
            content_type="text/plain",
            filename=_generate_smart_filename(request.filename, "diagram", "mmd")
        )
    elif request.format == "html":
        # Generate HTML wrapper
        html_content = _generate_html_wrapper(mermaid_content)
        return VisualizationResponse(
            success=True,
            content=html_content,
            content_type="text/html",
            filename=_generate_smart_filename(request.filename, "diagram", "html")
        )
    else:
        return VisualizationResponse(
            success=False,
            error=f"Format {request.format} not supported for Mermaid diagrams"
        )


async def _generate_graph_visualization(request: VisualizationRequest, parse_result) -> VisualizationResponse:
    """Generate control flow graph visualization."""
    # Generate CFG
    cfg = cfg_generator.generate_cfg(parse_result, request.function_name)
    
    # Set renderer theme
    flow_renderer.set_theme(request.theme)
    
    # Create temporary file for output
    session_id = str(uuid.uuid4())
    temp_path = Path(tempfile.gettempdir()) / f"flowlens_web_{session_id}.{request.format}"
    
    try:
        # Render to file
        flow_renderer.render_to_file(cfg, temp_path, request.format)
        
        # Read file and encode as base64
        with open(temp_path, 'rb') as f:
            content = base64.b64encode(f.read()).decode('utf-8')
        
        return VisualizationResponse(
            success=True,
            content=content,
            content_type=f"image/{request.format}",
            filename=_generate_smart_filename(request.filename, "cfg", request.format)
        )
        
    finally:
        # Clean up temporary file
        if temp_path.exists():
            temp_path.unlink()


async def _generate_heatmap_visualization(request: VisualizationRequest, parse_result) -> VisualizationResponse:
    """Generate variable heatmap visualization."""
    # For heatmap, we need execution data
    # This is a simplified version - in practice, you'd need actual execution
    
    return VisualizationResponse(
        success=False,
        error="Heatmap visualization requires execution data. This feature is coming soon!"
    )


@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """Handle file upload."""
    try:
        # Validate file type
        if not file.filename.endswith('.py'):
            raise HTTPException(status_code=400, detail="Only Python files (.py) are supported")
        
        # Read file content
        content = await file.read()
        code = content.decode('utf-8')
        
        # Track upload
        track_feature_usage('web_file_uploaded', {
            'filename': file.filename,
            'file_size': len(content),
            'code_lines': len(code.splitlines())
        })
        
        return {
            "success": True,
            "filename": file.filename,
            "code": code,
            "lines": len(code.splitlines())
        }
        
    except UnicodeDecodeError:
        raise HTTPException(status_code=400, detail="File must be valid UTF-8 encoded text")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@app.get("/api/tier")
async def get_tier_info():
    """Get current tier information and usage."""
    try:
        license_manager = get_license_manager()
        tier_info = license_manager.get_tier_info()

        # Create mock usage stats if method doesn't exist
        try:
            usage_stats = license_manager.get_usage_stats()
        except AttributeError:
            usage_stats = {
                "files_analyzed": 0,
                "exports_created": 0,
                "current_month": "2025-01"
            }

        return {
            "success": True,
            "tier": tier_info,
            "usage": usage_stats
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def _generate_html_wrapper(mermaid_content: str) -> str:
    """Generate HTML wrapper for Mermaid diagram."""
    return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlowLens Mermaid Diagram</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }}
        .diagram-container {{
            text-align: center;
            overflow: auto;
        }}
        .controls {{
            margin-bottom: 20px;
            text-align: center;
        }}
        .btn {{
            background: #0d6efd;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }}
        .btn:hover {{
            background: #0b5ed7;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FlowLens Code Visualization</h1>
            <p>Interactive Mermaid Diagram</p>
        </div>
        <div class="controls">
            <button class="btn" onclick="zoomIn()">Zoom In</button>
            <button class="btn" onclick="zoomOut()">Zoom Out</button>
            <button class="btn" onclick="resetZoom()">Reset</button>
            <button class="btn" onclick="downloadSVG()">Download SVG</button>
        </div>
        <div class="diagram-container" id="diagram">
            <div class="mermaid">
{mermaid_content}
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({{
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {{
                useMaxWidth: true,
                htmlLabels: true
            }}
        }});

        let currentZoom = 1;

        function zoomIn() {{
            currentZoom += 0.1;
            applyZoom();
        }}

        function zoomOut() {{
            currentZoom = Math.max(0.1, currentZoom - 0.1);
            applyZoom();
        }}

        function resetZoom() {{
            currentZoom = 1;
            applyZoom();
        }}

        function applyZoom() {{
            const diagram = document.querySelector('#diagram svg');
            if (diagram) {{
                diagram.style.transform = `scale(${{currentZoom}})`;
                diagram.style.transformOrigin = 'center center';
            }}
        }}

        function downloadSVG() {{
            const svg = document.querySelector('#diagram svg');
            if (svg) {{
                const svgData = new XMLSerializer().serializeToString(svg);
                const blob = new Blob([svgData], {{type: 'image/svg+xml'}});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'flowlens_diagram.svg';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }}
        }}
    </script>
</body>
</html>"""


def _generate_smart_filename(original_filename: str, suffix: str, extension: str) -> str:
    """
    Generate intelligent filename based on the original filename.

    Args:
        original_filename: Original filename (e.g., "calculator.py", "code.py")
        suffix: Suffix to add (e.g., "diagram", "cfg")
        extension: File extension (e.g., "mmd", "png", "html")

    Returns:
        Smart filename (e.g., "calculator_diagram.mmd", "code_diagram.png")
    """
    if not original_filename or original_filename == "code.py":
        # Default case for pasted code
        return f"code_{suffix}.{extension}"

    # Extract base name without extension
    base_name = Path(original_filename).stem

    # Handle special cases
    if base_name.lower() in ["untitled", "temp", "test"]:
        base_name = "code"

    # Clean the base name (remove special characters, spaces)
    import re
    base_name = re.sub(r'[^\w\-_]', '_', base_name)
    base_name = re.sub(r'_+', '_', base_name)  # Replace multiple underscores with single
    base_name = base_name.strip('_')  # Remove leading/trailing underscores

    if not base_name:
        base_name = "code"

    return f"{base_name}_{suffix}.{extension}"


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
