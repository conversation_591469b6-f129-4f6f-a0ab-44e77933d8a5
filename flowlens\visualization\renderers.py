"""
FlowLens Base Renderers

This module provides base classes and common functionality for all
FlowLens visualization renderers.

Features:
- Base renderer interface
- Common rendering utilities
- Output format handling
- Theme and styling management
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Optional, Dict, List
import matplotlib.pyplot as plt


class BaseRenderer(ABC):
    """
    Base class for all FlowLens renderers.
    
    Provides common functionality and interface for visualization
    components including output handling and styling.
    """
    
    def __init__(self):
        self.output_formats = ['png', 'svg', 'pdf']
        self.default_format = 'png'
        self.theme = 'default'
        self.style_config = {}
    
    @abstractmethod
    def render(self, data: Any, output_path: Optional[Path] = None) -> Optional[Any]:
        """Render the visualization."""
        pass
    
    def set_style(self, style_config: Dict[str, Any]) -> None:
        """Set custom style configuration."""
        self.style_config.update(style_config)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported output formats."""
        return self.output_formats.copy()
    
    def validate_format(self, format: str) -> bool:
        """Validate if format is supported."""
        return format.lower() in self.output_formats


class ImageRenderer(BaseRenderer):
    """
    Base class for image-based renderers using matplotlib.
    
    Provides common functionality for creating static image
    visualizations with matplotlib.
    """
    
    def __init__(self):
        super().__init__()
        self.figure_size = (12, 8)
        self.dpi = 100
        self.font_size = 10
        
    def create_figure(self, figsize: Optional[tuple] = None, dpi: Optional[int] = None) -> plt.Figure:
        """Create a matplotlib figure with standard settings."""
        figsize = figsize or self.figure_size
        dpi = dpi or self.dpi
        
        fig = plt.figure(figsize=figsize, dpi=dpi)
        return fig
    
    def save_figure(self, fig: plt.Figure, output_path: Path, format: Optional[str] = None) -> None:
        """Save matplotlib figure to file."""
        if format is None:
            format = output_path.suffix[1:].lower() or self.default_format
        
        if not self.validate_format(format):
            raise ValueError(f"Unsupported format: {format}")
        
        save_kwargs = {
            'dpi': self.dpi,
            'bbox_inches': 'tight',
            'pad_inches': 0.1,
            'format': format,
        }
        
        fig.savefig(output_path, **save_kwargs)


class InteractiveRenderer(BaseRenderer):
    """
    Base class for interactive renderers.
    
    Provides functionality for creating interactive visualizations
    that can be displayed in notebooks or web interfaces.
    """
    
    def __init__(self):
        super().__init__()
        self.output_formats.extend(['html', 'json'])
        self.interactive_backend = 'matplotlib'
    
    def set_backend(self, backend: str) -> None:
        """Set the interactive backend."""
        valid_backends = ['matplotlib', 'plotly', 'bokeh']
        if backend not in valid_backends:
            raise ValueError(f"Invalid backend: {backend}. Must be one of {valid_backends}")
        self.interactive_backend = backend
