"""
FlowLens Variable State Monitor

This module provides comprehensive variable state monitoring and analysis
for Python code execution. It tracks variable changes, generates heatmaps,
and provides insights into variable usage patterns.

Key Features:
- Real-time variable state tracking
- Variable change detection and history
- Variable usage pattern analysis
- Heatmap data generation for visualization
- Memory-efficient variable state storage
"""

import time
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import copy

from .execution_tracker import ExecutionStep, ExecutionResult


class VariableChangeType(Enum):
    """Types of variable changes."""
    CREATED = "created"
    MODIFIED = "modified"
    DELETED = "deleted"
    ACCESSED = "accessed"


@dataclass
class VariableChange:
    """Represents a change to a variable."""
    
    step_id: int
    line_number: int
    variable_name: str
    change_type: VariableChangeType
    old_value: Any = None
    new_value: Any = None
    timestamp: float = field(default_factory=time.time)
    
    @property
    def value_changed(self) -> bool:
        """Check if the value actually changed."""
        return self.old_value != self.new_value


@dataclass
class VariableState:
    """Current state of a variable."""
    
    name: str
    current_value: Any
    type_name: str
    first_seen_step: int
    last_modified_step: int
    access_count: int = 0
    modification_count: int = 0
    change_history: List[VariableChange] = field(default_factory=list)
    
    @property
    def age_steps(self) -> int:
        """Get age of variable in steps."""
        return self.last_modified_step - self.first_seen_step + 1
    
    @property
    def is_active(self) -> bool:
        """Check if variable is currently active."""
        return self.current_value is not None


@dataclass
class VariableHeatmapData:
    """Data for generating variable heatmaps."""
    
    variable_name: str
    line_numbers: List[int]
    access_counts: List[int]
    modification_counts: List[int]
    step_ranges: List[Tuple[int, int]]
    value_types: List[str]
    
    @property
    def total_accesses(self) -> int:
        """Get total access count."""
        return sum(self.access_counts)
    
    @property
    def total_modifications(self) -> int:
        """Get total modification count."""
        return sum(self.modification_counts)


class VariableMonitor:
    """
    Comprehensive variable state monitoring system.
    
    Tracks variable states throughout code execution and provides
    analysis capabilities for visualization and debugging.
    """
    
    def __init__(self, max_history_size: int = 10000):
        self.max_history_size = max_history_size
        self._variable_states: Dict[str, VariableState] = {}
        self._change_history: deque = deque(maxlen=max_history_size)
        self._step_variables: Dict[int, Dict[str, Any]] = {}
        self._line_variable_map: Dict[int, Set[str]] = defaultdict(set)
        self._monitoring_active = False
    
    def start_monitoring(self) -> None:
        """Start variable monitoring."""
        self._monitoring_active = True
        self._variable_states.clear()
        self._change_history.clear()
        self._step_variables.clear()
        self._line_variable_map.clear()
    
    def stop_monitoring(self) -> None:
        """Stop variable monitoring."""
        self._monitoring_active = False
    
    def process_execution_step(self, step: ExecutionStep) -> List[VariableChange]:
        """
        Process an execution step and detect variable changes.
        
        Args:
            step: Execution step to process
            
        Returns:
            List of variable changes detected in this step
        """
        if not self._monitoring_active:
            return []
        
        changes = []
        current_vars = step.all_variables
        previous_vars = self._step_variables.get(step.step_id - 1, {})
        
        # Store current step variables
        self._step_variables[step.step_id] = current_vars.copy()
        
        # Detect changes
        all_var_names = set(current_vars.keys()) | set(previous_vars.keys())
        
        for var_name in all_var_names:
            current_value = current_vars.get(var_name)
            previous_value = previous_vars.get(var_name)
            
            change = self._detect_variable_change(
                step, var_name, previous_value, current_value
            )
            
            if change:
                changes.append(change)
                self._change_history.append(change)
                self._update_variable_state(change)
                self._line_variable_map[step.line_number].add(var_name)
        
        return changes
    
    def process_execution_result(self, result: ExecutionResult) -> Dict[str, Any]:
        """
        Process complete execution result and generate analysis.
        
        Args:
            result: Complete execution result
            
        Returns:
            Dictionary containing variable analysis
        """
        # Process all steps
        all_changes = []
        for step in result.steps:
            changes = self.process_execution_step(step)
            all_changes.extend(changes)
        
        # Generate analysis
        analysis = {
            "total_variables": len(self._variable_states),
            "total_changes": len(all_changes),
            "variable_states": {name: self._serialize_variable_state(state) 
                              for name, state in self._variable_states.items()},
            "change_summary": self._generate_change_summary(),
            "heatmap_data": self.generate_heatmap_data(),
            "variable_lifetimes": self._calculate_variable_lifetimes(),
            "most_active_variables": self._get_most_active_variables(),
        }
        
        return analysis
    
    def get_variable_state(self, variable_name: str) -> Optional[VariableState]:
        """Get current state of a variable."""
        return self._variable_states.get(variable_name)
    
    def get_all_variable_states(self) -> Dict[str, VariableState]:
        """Get all current variable states."""
        return self._variable_states.copy()
    
    def get_variable_changes(self, variable_name: Optional[str] = None, 
                           limit: int = 100) -> List[VariableChange]:
        """
        Get variable changes history.
        
        Args:
            variable_name: Optional variable name to filter by
            limit: Maximum number of changes to return
            
        Returns:
            List of variable changes
        """
        changes = list(self._change_history)
        
        if variable_name:
            changes = [c for c in changes if c.variable_name == variable_name]
        
        return changes[-limit:]
    
    def generate_heatmap_data(self) -> Dict[str, VariableHeatmapData]:
        """
        Generate heatmap data for all variables.
        
        Returns:
            Dictionary mapping variable names to heatmap data
        """
        heatmap_data = {}
        
        for var_name, var_state in self._variable_states.items():
            # Collect line-based statistics
            line_stats = defaultdict(lambda: {"access": 0, "modify": 0, "steps": []})
            
            for change in var_state.change_history:
                line_num = change.line_number
                line_stats[line_num]["steps"].append(change.step_id)
                
                if change.change_type == VariableChangeType.ACCESSED:
                    line_stats[line_num]["access"] += 1
                elif change.change_type in [VariableChangeType.CREATED, VariableChangeType.MODIFIED]:
                    line_stats[line_num]["modify"] += 1
            
            # Create heatmap data
            if line_stats:
                lines = sorted(line_stats.keys())
                heatmap_data[var_name] = VariableHeatmapData(
                    variable_name=var_name,
                    line_numbers=lines,
                    access_counts=[line_stats[line]["access"] for line in lines],
                    modification_counts=[line_stats[line]["modify"] for line in lines],
                    step_ranges=[(min(line_stats[line]["steps"]), max(line_stats[line]["steps"])) 
                               for line in lines],
                    value_types=[var_state.type_name] * len(lines),
                )
        
        return heatmap_data
    
    def get_variable_usage_pattern(self, variable_name: str) -> Dict[str, Any]:
        """
        Get detailed usage pattern for a specific variable.
        
        Args:
            variable_name: Name of the variable to analyze
            
        Returns:
            Dictionary containing usage pattern analysis
        """
        var_state = self._variable_states.get(variable_name)
        if not var_state:
            return {}
        
        changes = var_state.change_history
        
        # Analyze patterns
        access_intervals = []
        modify_intervals = []
        
        last_access_step = None
        last_modify_step = None
        
        for change in changes:
            if change.change_type == VariableChangeType.ACCESSED:
                if last_access_step is not None:
                    access_intervals.append(change.step_id - last_access_step)
                last_access_step = change.step_id
            
            elif change.change_type in [VariableChangeType.CREATED, VariableChangeType.MODIFIED]:
                if last_modify_step is not None:
                    modify_intervals.append(change.step_id - last_modify_step)
                last_modify_step = change.step_id
        
        return {
            "variable_name": variable_name,
            "total_changes": len(changes),
            "access_count": var_state.access_count,
            "modification_count": var_state.modification_count,
            "lifetime_steps": var_state.age_steps,
            "avg_access_interval": sum(access_intervals) / len(access_intervals) if access_intervals else 0,
            "avg_modify_interval": sum(modify_intervals) / len(modify_intervals) if modify_intervals else 0,
            "lines_used": list(set(change.line_number for change in changes)),
            "value_types_seen": list(set(type(change.new_value).__name__ for change in changes 
                                       if change.new_value is not None)),
        }
    
    def _detect_variable_change(self, step: ExecutionStep, var_name: str, 
                               old_value: Any, new_value: Any) -> Optional[VariableChange]:
        """Detect and classify variable change."""
        if old_value is None and new_value is not None:
            # Variable created
            return VariableChange(
                step_id=step.step_id,
                line_number=step.line_number,
                variable_name=var_name,
                change_type=VariableChangeType.CREATED,
                old_value=old_value,
                new_value=new_value,
            )
        elif old_value is not None and new_value is None:
            # Variable deleted
            return VariableChange(
                step_id=step.step_id,
                line_number=step.line_number,
                variable_name=var_name,
                change_type=VariableChangeType.DELETED,
                old_value=old_value,
                new_value=new_value,
            )
        elif old_value != new_value and new_value is not None:
            # Variable modified
            return VariableChange(
                step_id=step.step_id,
                line_number=step.line_number,
                variable_name=var_name,
                change_type=VariableChangeType.MODIFIED,
                old_value=old_value,
                new_value=new_value,
            )
        elif old_value == new_value and new_value is not None:
            # Variable accessed (same value)
            return VariableChange(
                step_id=step.step_id,
                line_number=step.line_number,
                variable_name=var_name,
                change_type=VariableChangeType.ACCESSED,
                old_value=old_value,
                new_value=new_value,
            )
        
        return None
    
    def _update_variable_state(self, change: VariableChange) -> None:
        """Update variable state based on change."""
        var_name = change.variable_name
        
        if var_name not in self._variable_states:
            # Create new variable state
            self._variable_states[var_name] = VariableState(
                name=var_name,
                current_value=change.new_value,
                type_name=type(change.new_value).__name__ if change.new_value is not None else "NoneType",
                first_seen_step=change.step_id,
                last_modified_step=change.step_id,
            )
        
        var_state = self._variable_states[var_name]
        
        # Update state
        if change.change_type == VariableChangeType.ACCESSED:
            var_state.access_count += 1
        elif change.change_type in [VariableChangeType.CREATED, VariableChangeType.MODIFIED]:
            var_state.modification_count += 1
            var_state.current_value = change.new_value
            var_state.type_name = type(change.new_value).__name__ if change.new_value is not None else "NoneType"
            var_state.last_modified_step = change.step_id
        elif change.change_type == VariableChangeType.DELETED:
            var_state.current_value = None
            var_state.last_modified_step = change.step_id
        
        # Add to change history
        var_state.change_history.append(change)
    
    def _serialize_variable_state(self, state: VariableState) -> Dict[str, Any]:
        """Serialize variable state for JSON output."""
        return {
            "name": state.name,
            "current_value": self._safe_serialize_value(state.current_value),
            "type_name": state.type_name,
            "first_seen_step": state.first_seen_step,
            "last_modified_step": state.last_modified_step,
            "access_count": state.access_count,
            "modification_count": state.modification_count,
            "age_steps": state.age_steps,
            "is_active": state.is_active,
        }
    
    def _safe_serialize_value(self, value: Any) -> Any:
        """Safely serialize a value for JSON output."""
        if value is None or isinstance(value, (int, float, str, bool)):
            return value
        elif isinstance(value, (list, tuple)):
            if len(str(value)) < 200:
                return list(value)
            else:
                return f"<{type(value).__name__} of length {len(value)}>"
        elif isinstance(value, dict):
            if len(str(value)) < 200:
                return dict(value)
            else:
                return f"<dict with {len(value)} items>"
        else:
            return f"<{type(value).__name__}>"
    
    def _generate_change_summary(self) -> Dict[str, int]:
        """Generate summary of all changes."""
        summary = defaultdict(int)
        
        for change in self._change_history:
            summary[change.change_type.value] += 1
        
        return dict(summary)
    
    def _calculate_variable_lifetimes(self) -> Dict[str, int]:
        """Calculate lifetime of each variable in steps."""
        lifetimes = {}
        
        for var_name, var_state in self._variable_states.items():
            lifetimes[var_name] = var_state.age_steps
        
        return lifetimes
    
    def _get_most_active_variables(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most active variables by total activity."""
        variable_activity = []
        
        for var_name, var_state in self._variable_states.items():
            activity_score = var_state.access_count + var_state.modification_count * 2
            variable_activity.append({
                "name": var_name,
                "activity_score": activity_score,
                "access_count": var_state.access_count,
                "modification_count": var_state.modification_count,
            })
        
        # Sort by activity score
        variable_activity.sort(key=lambda x: x["activity_score"], reverse=True)
        
        return variable_activity[:limit]
