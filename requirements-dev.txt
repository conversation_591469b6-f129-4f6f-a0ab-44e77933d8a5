# FlowLens - Development Dependencies

# Testing framework
pytest>=7.2.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-benchmark>=4.0.0
pytest-xdist>=3.1.0
pytest-asyncio>=0.21.0

# Code quality and linting
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0
pylint>=2.15.0
bandit>=1.7.4

# Documentation
sphinx>=5.3.0
sphinx-rtd-theme>=1.2.0
myst-parser>=0.18.0
sphinx-click>=4.4.0

# Development tools
pre-commit>=3.0.0
tox>=4.0.0
twine>=4.0.0
build>=0.10.0

# Profiling and debugging
line-profiler>=4.0.0
py-spy>=0.3.14
pdb++>=0.10.3

# Type checking
types-PyYAML>=6.0.0
types-toml>=0.10.0
types-colorama>=0.4.0

# Jupyter for development and examples
jupyter>=1.0.0
ipykernel>=6.20.0
notebook>=6.5.0
