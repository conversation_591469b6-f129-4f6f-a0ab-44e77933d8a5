[metadata]
name = flowlens
version = 1.0.0
author = <PERSON>
author_email = 31132150+Hector<PERSON><EMAIL>
description = Interactive Python Code Visualization & Execution Analysis
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/HectorTa1989/FlowLens
project_urls =
    Bug Tracker = https://github.com/HectorTa1989/FlowLens/issues
    Documentation = https://docs.flowlens.com
    Source = https://github.com/HectorTa1989/FlowLens
classifiers =
    Development Status :: 4 - Beta
    Intended Audience :: Developers
    Intended Audience :: Education
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Topic :: Software Development :: Debuggers
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: Scientific/Engineering :: Visualization
    Topic :: Education
    Environment :: Console

[options]
package_dir =
    = .
packages = find:
python_requires = >=3.8
install_requires =
    click>=8.1.0
    rich>=13.0.0
    colorama>=0.4.6
    networkx>=3.0
    matplotlib>=3.6.0
    graphviz>=0.20.0
    numpy>=1.21.0
    psutil>=5.9.0
    pyyaml>=6.0
    prompt-toolkit>=3.0.0
    pygments>=2.13.0
    chardet>=5.0.0
    pathlib2>=2.3.7; python_version < "3.9"
    typing-extensions>=4.0.0; python_version < "3.10"

[options.packages.find]
where = .

[options.entry_points]
console_scripts =
    flowlens = flowlens.cli.main:main
    fl = flowlens.cli.main:main

[options.extras_require]
dev =
    pytest>=7.2.0
    pytest-cov>=4.0.0
    pytest-mock>=3.10.0
    pytest-benchmark>=4.0.0
    black>=23.0.0
    isort>=5.12.0
    flake8>=6.0.0
    mypy>=1.0.0
    pylint>=2.15.0
    bandit>=1.7.4
    pre-commit>=3.0.0
docs =
    sphinx>=4.0.0
    sphinx-rtd-theme>=1.0.0
    myst-parser>=0.17.0
test =
    pytest>=7.0.0
    pytest-cov>=4.0.0
    pytest-mock>=3.10.0
    pytest-benchmark>=4.0.0

[bdist_wheel]
universal = 0

[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = .git,__pycache__,build,dist,.eggs

[mypy]
python_version = 3.8
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
check_untyped_defs = true
ignore_missing_imports = true

[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
