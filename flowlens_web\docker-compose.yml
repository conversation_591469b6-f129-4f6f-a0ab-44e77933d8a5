# FlowLens Web Application Docker Compose Configuration

version: '3.8'

services:
  flowlens-web:
    build:
      context: ..
      dockerfile: flowlens_web/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=info
    volumes:
      # Mount for development (comment out for production)
      - ./flowlens_web:/app/flowlens_web
      - ../flowlens:/app/flowlens
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add Redis for session storage and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Optional: Add PostgreSQL for user data and analytics
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: flowlens_web
      POSTGRES_USER: flowlens
      POSTGRES_PASSWORD: flowlens_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:

networks:
  default:
    name: flowlens-network
