Build a Python CLI application called FlowLens that provides interactive code visualization and execution analysis. The application should:

**Core Requirements:**
1. Create a Python CLI tool that parses Python code using AST (Abstract Syntax Tree)
2. Provide real-time step-through visualization of code execution
3. Generate interactive diagrams showing code flow and control flow graphs (CFG)
4. Track variable states throughout execution with visual heatmaps
5. Achieve <200ms visualization latency for 100 lines of code
6. Maintain 95% accuracy in execution path prediction

**Technical Specifications:**
- Use Python's built-in `ast` module for code parsing
- Implement custom algorithms (no paid APIs)
- Use free visualization libraries (matplotlib, networkx, graphviz, etc.)
- Create a command-line interface with interactive features
- Support common Python constructs (functions, loops, conditionals, classes)

**Deliverables:**
1. **GitHub README.md** containing:
   - 3-5 alternative product names with available domain names (verify availability)
   - System architecture diagram in Mermaid syntax
   - Workflow diagram in Mermaid syntax
   - Complete project structure with file organization
   - Installation and usage instructions

2. **Complete source code** with:
   - Each file provided as a separate code block with exact file path
   - Individual commit messages for each file
   - Proper Python project structure (setup.py, requirements.txt, etc.)
   - Unit tests for core functionality
   - Documentation and examples

**GitHub Integration:**
- Target repository: https://github.com/HectorTa1989
- Ensure all suggested product names have available .com domains
- Include proper Python packaging structure for PyPI distribution
- Add CI/CD configuration files (GitHub Actions)

**Technical Constraints:**
- Use only free and open-source dependencies
- Implement core visualization algorithms from scratch
- Ensure cross-platform compatibility (Windows, macOS, Linux)
- Support Python 3.8+ versions