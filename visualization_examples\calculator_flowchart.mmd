---
title: Calculator.multiply() Method
---
flowchart TD
    node_1(["START"])
    node_2["L17: 'Multiply two numbers.'"]
    node_3{L18: if a == 0 or b == 0}
    node_4["L19: return 0"]
    node_5["L18: merge"]
    node_6["L21: result = a * b"]
    node_7["L22: self.history.append(f'{a} *..."]
    node_8["L23: return result"]
    node_1 --> node_2
    node_2 --> node_3
    node_3 --> node_4
    node_3 -->|True| node_3
    node_3 -->|False| node_5
    node_5 --> node_6
    node_6 --> node_7
    node_7 --> node_8
    classDef entry fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef exit fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    classDef condition fill:#ffe0b2,stroke:#ff9800,stroke-width:2px
    classDef statement fill:#bbdefb,stroke:#2196f3,stroke-width:2px
    classDef loop_header fill:#e1bee7,stroke:#9c27b0,stroke-width:2px