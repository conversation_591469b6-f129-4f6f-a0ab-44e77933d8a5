# FlowLens Web Application Requirements

# Core FlowLens dependencies (from parent package)
# These should be installed from the main FlowLens package
# pip install -e ../

# Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Template Engine
jinja2>=3.1.2

# File Upload Support
python-multipart>=0.0.6

# Additional Web Dependencies
aiofiles>=23.2.1
python-jose[cryptography]>=3.3.0  # For JWT tokens if needed
passlib[bcrypt]>=1.7.4  # For password hashing if needed

# Development Dependencies (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
httpx>=0.25.0  # For testing FastAPI endpoints
