#!/usr/bin/env python3
"""
FlowLens Advanced Features Example

This example demonstrates advanced FlowLens features including:
- Custom theme creation
- Performance monitoring
- Variable tracking
- Complex code analysis
- Visualization customization
"""

import time
import asyncio
from pathlib import Path
from typing import List, Dict, Any

from flowlens import <PERSON>Analyzer, Visualizer
from flowlens.core import CFGGenerator, ExecutionTracker, VariableMonitor
from flowlens.config import get_theme, create_custom_theme
from flowlens.utils.performance import PerformanceMonitor


class DataProcessor:
    """Example class for advanced analysis."""
    
    def __init__(self, data: List[Dict[str, Any]]):
        self.data = data
        self.processed_count = 0
        self.errors = []
    
    def process_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single data item."""
        try:
            if 'value' not in item:
                raise ValueError("Missing 'value' field")
            
            processed = {
                'original': item,
                'processed_value': item['value'] * 2,
                'timestamp': time.time(),
                'status': 'success'
            }
            
            self.processed_count += 1
            return processed
            
        except Exception as e:
            error_info = {
                'item': item,
                'error': str(e),
                'timestamp': time.time(),
                'status': 'error'
            }
            self.errors.append(error_info)
            return error_info
    
    def process_batch(self, batch_size: int = 10) -> List[Dict[str, Any]]:
        """Process data in batches."""
        results = []
        
        for i in range(0, len(self.data), batch_size):
            batch = self.data[i:i + batch_size]
            batch_results = []
            
            for item in batch:
                result = self.process_item(item)
                batch_results.append(result)
            
            results.extend(batch_results)
            
            # Simulate processing delay
            time.sleep(0.01)
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics."""
        total_items = len(self.data)
        error_rate = len(self.errors) / total_items if total_items > 0 else 0
        
        return {
            'total_items': total_items,
            'processed_count': self.processed_count,
            'error_count': len(self.errors),
            'error_rate': error_rate,
            'success_rate': 1 - error_rate
        }


def fibonacci_generator(n: int):
    """Generator function for Fibonacci sequence."""
    a, b = 0, 1
    count = 0
    
    while count < n:
        yield a
        a, b = b, a + b
        count += 1


async def async_data_fetcher(urls: List[str]) -> List[Dict[str, Any]]:
    """Async function for data fetching simulation."""
    results = []
    
    for url in urls:
        # Simulate async operation
        await asyncio.sleep(0.1)
        
        result = {
            'url': url,
            'status': 'success' if 'valid' in url else 'error',
            'data': f"Data from {url}" if 'valid' in url else None,
            'timestamp': time.time()
        }
        
        results.append(result)
    
    return results


def complex_algorithm(data: List[int]) -> Dict[str, Any]:
    """Complex algorithm with multiple control structures."""
    if not data:
        return {'error': 'Empty data'}
    
    # Initialize variables
    result = {
        'original_data': data.copy(),
        'sorted_data': [],
        'statistics': {},
        'operations': []
    }
    
    # Bubble sort with tracking
    sorted_data = data.copy()
    n = len(sorted_data)
    
    for i in range(n):
        swapped = False
        
        for j in range(0, n - i - 1):
            if sorted_data[j] > sorted_data[j + 1]:
                # Swap elements
                sorted_data[j], sorted_data[j + 1] = sorted_data[j + 1], sorted_data[j]
                swapped = True
                
                result['operations'].append({
                    'type': 'swap',
                    'positions': [j, j + 1],
                    'values': [sorted_data[j + 1], sorted_data[j]]
                })
        
        if not swapped:
            break
    
    result['sorted_data'] = sorted_data
    
    # Calculate statistics
    result['statistics'] = {
        'min': min(data),
        'max': max(data),
        'sum': sum(data),
        'average': sum(data) / len(data),
        'length': len(data),
        'operations_count': len(result['operations'])
    }
    
    # Find patterns
    patterns = []
    
    for i in range(len(data) - 1):
        if data[i] < data[i + 1]:
            patterns.append('ascending')
        elif data[i] > data[i + 1]:
            patterns.append('descending')
        else:
            patterns.append('equal')
    
    result['patterns'] = patterns
    
    return result


def demonstrate_advanced_analysis():
    """Demonstrate advanced FlowLens analysis features."""
    print("FlowLens Advanced Features Demo")
    print("=" * 50)
    
    # Create sample code for analysis
    sample_code = '''
def advanced_function(data, threshold=10):
    """Advanced function with complex logic."""
    results = []
    
    try:
        for i, item in enumerate(data):
            if item > threshold:
                # Process high-value items
                processed = item ** 2
                
                if processed > 100:
                    results.append({
                        'index': i,
                        'original': item,
                        'processed': processed,
                        'category': 'high'
                    })
                else:
                    results.append({
                        'index': i,
                        'original': item,
                        'processed': processed,
                        'category': 'medium'
                    })
            else:
                # Process low-value items
                if item > 0:
                    results.append({
                        'index': i,
                        'original': item,
                        'processed': item,
                        'category': 'low'
                    })
    
    except Exception as e:
        return {'error': str(e)}
    
    finally:
        print(f"Processed {len(results)} items")
    
    return results

# Test the function
test_data = [1, 5, 15, 25, 3, 12, 8, 20]
result = advanced_function(test_data)
print(f"Result: {result}")
    '''
    
    # 1. Advanced parsing with performance monitoring
    print("\n1. Advanced Parsing with Performance Monitoring")
    print("-" * 45)
    
    performance_monitor = PerformanceMonitor()
    analyzer = FlowAnalyzer()
    
    # Analyze with performance tracking
    with performance_monitor.performance_timer(performance_monitor, "advanced_analysis"):
        parse_result = analyzer.analyze_code(sample_code)
    
    print(f"Parse time: {parse_result.parse_time:.3f}s")
    print(f"Functions found: {len(parse_result.functions)}")
    print(f"Decision points: {len(parse_result.decision_points)}")
    print(f"Complexity score: {parse_result.complexity_score}")
    
    # Show performance metrics
    stats = performance_monitor.get_operation_stats("advanced_analysis")
    if stats:
        print(f"Performance: {stats['avg_duration_ms']:.2f}ms average")
    
    # 2. Control Flow Graph with custom visualization
    print("\n2. Control Flow Graph Generation")
    print("-" * 35)
    
    cfg_generator = CFGGenerator()
    cfg = cfg_generator.generate_cfg(parse_result, "advanced_function")
    
    metrics = cfg.get_complexity_metrics()
    print(f"CFG Nodes: {metrics['nodes']}")
    print(f"CFG Edges: {metrics['edges']}")
    print(f"Cyclomatic Complexity: {metrics['cyclomatic_complexity']}")
    
    # 3. Execution tracking with variable monitoring
    print("\n3. Execution Tracking and Variable Monitoring")
    print("-" * 45)
    
    tracker = ExecutionTracker(performance_monitor)
    monitor = VariableMonitor()
    
    # Start tracking
    execution_id = tracker.start_tracking(sample_code, cfg)
    
    # Run execution
    execution_result = tracker.run_to_completion(execution_id, max_steps=1000)
    
    print(f"Execution completed in {execution_result.duration_ms:.2f}ms")
    print(f"Total steps: {execution_result.total_steps}")
    print(f"Final state: {execution_result.final_state.value}")
    
    # Analyze variables
    monitor.start_monitoring()
    variable_analysis = monitor.process_execution_result(execution_result)
    
    print(f"Variables tracked: {variable_analysis['total_variables']}")
    print(f"Variable changes: {variable_analysis['total_changes']}")
    
    # Show most active variables
    if variable_analysis['most_active_variables']:
        print("\nMost active variables:")
        for var in variable_analysis['most_active_variables'][:3]:
            print(f"  - {var['name']}: {var['activity_score']} activity score")
    
    # 4. Custom theme creation
    print("\n4. Custom Theme Creation")
    print("-" * 25)
    
    # Create custom theme
    custom_theme = create_custom_theme(
        name="demo_theme",
        base_theme="default",
        description="Custom theme for demo",
        node_colors={
            "entry": "#00FF00",
            "exit": "#FF0000",
            "statement": "#0000FF",
        },
        background_color="#F0F0F0"
    )
    
    print(f"Created custom theme: {custom_theme.name}")
    print(f"Description: {custom_theme.description}")
    
    # 5. Visualization with custom settings
    print("\n5. Advanced Visualization")
    print("-" * 25)
    
    visualizer = Visualizer()
    
    # Note: In a real scenario, you would save actual visualizations
    print("Visualizer configured with custom theme")
    print("Ready to generate flow diagrams and heatmaps")
    
    # 6. Performance report
    print("\n6. Performance Report")
    print("-" * 20)
    
    report = performance_monitor.get_performance_report()
    print(f"Total operations: {report['total_operations']}")
    
    for op_name, stats in report['operation_stats'].items():
        if stats:
            print(f"{op_name}: {stats['avg_duration_ms']:.2f}ms avg")
    
    # Check latency compliance
    for op_name, compliant in report['latency_compliance'].items():
        status = "✓" if compliant else "✗"
        print(f"{status} {op_name}: {'Compliant' if compliant else 'Non-compliant'}")


def demonstrate_real_world_example():
    """Demonstrate FlowLens with real-world code example."""
    print("\n" + "=" * 50)
    print("Real-World Example: Data Processing Pipeline")
    print("=" * 50)
    
    # Create sample data
    sample_data = [
        {'id': 1, 'value': 10, 'category': 'A'},
        {'id': 2, 'value': 25, 'category': 'B'},
        {'id': 3, 'value': 5, 'category': 'A'},
        {'id': 4, 'category': 'C'},  # Missing 'value' - will cause error
        {'id': 5, 'value': 30, 'category': 'B'},
    ]
    
    # Process data
    processor = DataProcessor(sample_data)
    results = processor.process_batch(batch_size=2)
    stats = processor.get_statistics()
    
    print(f"Processed {stats['processed_count']} items")
    print(f"Error rate: {stats['error_rate']:.2%}")
    print(f"Success rate: {stats['success_rate']:.2%}")
    
    # Analyze the processor code
    processor_code = '''
class DataProcessor:
    def __init__(self, data):
        self.data = data
        self.processed_count = 0
        self.errors = []
    
    def process_item(self, item):
        try:
            if 'value' not in item:
                raise ValueError("Missing 'value' field")
            
            processed = {
                'original': item,
                'processed_value': item['value'] * 2,
                'status': 'success'
            }
            
            self.processed_count += 1
            return processed
            
        except Exception as e:
            error_info = {
                'item': item,
                'error': str(e),
                'status': 'error'
            }
            self.errors.append(error_info)
            return error_info
    '''
    
    # Analyze with FlowLens
    analyzer = FlowAnalyzer()
    result = analyzer.analyze_code(processor_code)
    
    print(f"\nCode Analysis:")
    print(f"Classes: {len(result.classes)}")
    print(f"Methods: {len(result.functions)}")
    print(f"Exception handlers: {len(result.exception_handlers)}")
    print(f"Complexity: {result.complexity_score}")


if __name__ == "__main__":
    demonstrate_advanced_analysis()
    demonstrate_real_world_example()
    
    print("\n" + "=" * 50)
    print("Advanced Features Demo Complete!")
    print("=" * 50)
