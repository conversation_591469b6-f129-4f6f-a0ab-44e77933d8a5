---
title: Control Flow: test_sample.py
---
flowchart TD
    node_1(["START"])
    node_2["L1: import json as _json"]
    node_3(["END"])
    node_1 --> node_2
    node_2 --> node_3
    classDef entry fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef exit fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    classDef condition fill:#ffe0b2,stroke:#ff9800,stroke-width:2px
    classDef statement fill:#bbdefb,stroke:#2196f3,stroke-width:2px
    classDef loop_header fill:#e1bee7,stroke:#9c27b0,stroke-width:2px