"""
FlowLens Heatmap Renderer

This module provides variable state heatmap visualization using matplotlib.
It creates visual representations of variable usage patterns, access frequency,
and modification patterns throughout code execution.

Key Features:
- Variable access frequency heatmaps
- Variable modification pattern visualization
- Multi-variable comparison heatmaps
- Customizable color schemes and scales
- High-quality output formats
"""

import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings

# Suppress matplotlib warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

from ..core.variable_monitor import VariableHeatmapData
from ..core.analytics import track_feature_usage, track_error
from ..core.licensing import get_license_manager, check_feature_access
from .renderers import BaseRenderer


class HeatmapRenderer(BaseRenderer):
    """
    Renders variable state heatmaps for code execution analysis.
    
    Creates visual representations of variable usage patterns,
    showing access frequency, modification patterns, and temporal
    distribution of variable activity.
    """
    
    def __init__(self):
        super().__init__()
        self.colormap = 'viridis'
        self.figure_size = (12, 8)
        self.dpi = 100
        self.font_size = 10
        self.show_colorbar = True
        self.show_grid = True
        self.interpolation = 'nearest'
        
    def set_colormap(self, colormap: str) -> None:
        """Set the colormap for heatmap visualization."""
        # Validate colormap
        try:
            plt.cm.get_cmap(colormap)
            self.colormap = colormap
        except ValueError:
            raise ValueError(f"Invalid colormap: {colormap}")
    
    def render(self, heatmap_data: Dict[str, VariableHeatmapData], 
              output_path: Optional[Path] = None) -> Optional[plt.Figure]:
        """
        Render variable heatmap visualization.
        
        Args:
            heatmap_data: Dictionary of variable heatmap data
            output_path: Optional path to save the image
            
        Returns:
            matplotlib Figure object if no output_path, None otherwise
        """
        if not heatmap_data:
            raise ValueError("No heatmap data provided")
        
        # Determine layout based on number of variables
        num_vars = len(heatmap_data)
        
        if num_vars == 1:
            return self._render_single_variable(list(heatmap_data.values())[0], output_path)
        else:
            return self._render_multiple_variables(heatmap_data, output_path)
    
    def render_to_file(self, heatmap_data: Dict[str, VariableHeatmapData],
                      output_path: Path, format: str = "png") -> None:
        """Render heatmap to file with licensing checks."""
        if format not in ["png", "svg", "pdf"]:
            raise ValueError(f"Unsupported format: {format}")

        # Check export format licensing
        can_export, upgrade_prompt = check_feature_access('export', {'format': format})
        if not can_export:
            # Track blocked export attempt
            track_feature_usage('export_blocked', {
                'format': format,
                'reason': 'tier_limit',
                'output_type': 'heatmap'
            })

            # Record the attempt for analytics
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=False)

            raise ValueError(f"Export format '{format}' not available in your tier. {upgrade_prompt.message}")

        # Track successful export attempt
        track_feature_usage('export_started', {
            'format': format,
            'output_type': 'heatmap',
            'variables_count': len(heatmap_data)
        })

        # Ensure output path has correct extension
        if not output_path.suffix:
            output_path = output_path.with_suffix(f".{format}")

        try:
            self.render(heatmap_data, output_path)

            # Record successful export
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=True)

            track_feature_usage('export_completed', {
                'format': format,
                'output_type': 'heatmap',
                'variables_count': len(heatmap_data),
                'file_size_kb': output_path.stat().st_size / 1024 if output_path.exists() else 0
            })

        except Exception as e:
            # Track export error
            track_error('ExportError', str(e), {
                'format': format,
                'output_type': 'heatmap'
            })

            # Record failed export
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=False)

            raise
    
    def _render_single_variable(self, data: VariableHeatmapData, 
                               output_path: Optional[Path] = None) -> Optional[plt.Figure]:
        """Render heatmap for a single variable."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.figure_size, dpi=self.dpi)
        
        # Prepare data matrices
        lines = np.array(data.line_numbers)
        accesses = np.array(data.access_counts)
        modifications = np.array(data.modification_counts)
        
        # Create heatmap matrices
        access_matrix = self._create_line_matrix(lines, accesses)
        modify_matrix = self._create_line_matrix(lines, modifications)
        
        # Plot access heatmap
        im1 = ax1.imshow(access_matrix, cmap=self.colormap, aspect='auto', 
                        interpolation=self.interpolation)
        ax1.set_title(f'{data.variable_name} - Access Frequency', fontsize=self.font_size + 2)
        ax1.set_ylabel('Line Number', fontsize=self.font_size)
        ax1.set_xlabel('Execution Timeline', fontsize=self.font_size)
        
        # Set y-axis labels to line numbers
        if len(lines) <= 20:  # Only show labels if not too many
            ax1.set_yticks(range(len(lines)))
            ax1.set_yticklabels(lines)
        
        if self.show_colorbar:
            plt.colorbar(im1, ax=ax1, label='Access Count')
        
        # Plot modification heatmap
        im2 = ax2.imshow(modify_matrix, cmap=self.colormap, aspect='auto', 
                        interpolation=self.interpolation)
        ax2.set_title(f'{data.variable_name} - Modification Frequency', fontsize=self.font_size + 2)
        ax2.set_ylabel('Line Number', fontsize=self.font_size)
        ax2.set_xlabel('Execution Timeline', fontsize=self.font_size)
        
        if len(lines) <= 20:
            ax2.set_yticks(range(len(lines)))
            ax2.set_yticklabels(lines)
        
        if self.show_colorbar:
            plt.colorbar(im2, ax=ax2, label='Modification Count')
        
        # Add grid if requested
        if self.show_grid:
            ax1.grid(True, alpha=0.3)
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save or return
        if output_path:
            self._save_figure(fig, output_path)
            plt.close(fig)
            return None
        else:
            return fig
    
    def _render_multiple_variables(self, heatmap_data: Dict[str, VariableHeatmapData], 
                                  output_path: Optional[Path] = None) -> Optional[plt.Figure]:
        """Render heatmap for multiple variables."""
        num_vars = len(heatmap_data)
        
        # Calculate subplot layout
        cols = min(3, num_vars)
        rows = (num_vars + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(self.figure_size[0] * cols / 2, 
                                                     self.figure_size[1] * rows / 2), 
                                dpi=self.dpi)
        
        # Ensure axes is always 2D
        if rows == 1 and cols == 1:
            axes = np.array([[axes]])
        elif rows == 1:
            axes = axes.reshape(1, -1)
        elif cols == 1:
            axes = axes.reshape(-1, 1)
        
        # Plot each variable
        for i, (var_name, data) in enumerate(heatmap_data.items()):
            row = i // cols
            col = i % cols
            ax = axes[row, col]
            
            # Combine access and modification data
            lines = np.array(data.line_numbers)
            combined_activity = np.array(data.access_counts) + np.array(data.modification_counts) * 2
            
            # Create activity matrix
            activity_matrix = self._create_line_matrix(lines, combined_activity)
            
            # Plot heatmap
            im = ax.imshow(activity_matrix, cmap=self.colormap, aspect='auto', 
                          interpolation=self.interpolation)
            ax.set_title(f'{var_name}', fontsize=self.font_size)
            ax.set_ylabel('Line', fontsize=self.font_size - 1)
            ax.set_xlabel('Time', fontsize=self.font_size - 1)
            
            # Simplified labels for multiple variables
            if len(lines) <= 10:
                ax.set_yticks(range(len(lines)))
                ax.set_yticklabels(lines, fontsize=self.font_size - 2)
            
            if self.show_grid:
                ax.grid(True, alpha=0.3)
        
        # Hide unused subplots
        for i in range(num_vars, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].set_visible(False)
        
        # Add overall title
        fig.suptitle('Variable Activity Heatmaps', fontsize=self.font_size + 4, fontweight='bold')
        
        plt.tight_layout()
        
        # Save or return
        if output_path:
            self._save_figure(fig, output_path)
            plt.close(fig)
            return None
        else:
            return fig
    
    def render_comparison_heatmap(self, heatmap_data: Dict[str, VariableHeatmapData], 
                                 output_path: Optional[Path] = None) -> Optional[plt.Figure]:
        """Render a comparison heatmap showing all variables together."""
        if not heatmap_data:
            raise ValueError("No heatmap data provided")
        
        # Collect all line numbers and variables
        all_lines = set()
        for data in heatmap_data.values():
            all_lines.update(data.line_numbers)
        
        sorted_lines = sorted(all_lines)
        var_names = list(heatmap_data.keys())
        
        # Create comparison matrix
        comparison_matrix = np.zeros((len(sorted_lines), len(var_names)))
        
        for j, var_name in enumerate(var_names):
            data = heatmap_data[var_name]
            for i, line in enumerate(sorted_lines):
                if line in data.line_numbers:
                    line_idx = data.line_numbers.index(line)
                    activity = data.access_counts[line_idx] + data.modification_counts[line_idx] * 2
                    comparison_matrix[i, j] = activity
        
        # Create figure
        fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
        
        # Plot heatmap
        im = ax.imshow(comparison_matrix, cmap=self.colormap, aspect='auto', 
                      interpolation=self.interpolation)
        
        # Set labels
        ax.set_title('Variable Activity Comparison', fontsize=self.font_size + 4, fontweight='bold')
        ax.set_ylabel('Line Number', fontsize=self.font_size)
        ax.set_xlabel('Variables', fontsize=self.font_size)
        
        # Set ticks and labels
        ax.set_xticks(range(len(var_names)))
        ax.set_xticklabels(var_names, rotation=45, ha='right')
        
        if len(sorted_lines) <= 30:
            ax.set_yticks(range(len(sorted_lines)))
            ax.set_yticklabels(sorted_lines)
        
        # Add colorbar
        if self.show_colorbar:
            plt.colorbar(im, ax=ax, label='Activity Level')
        
        # Add grid
        if self.show_grid:
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save or return
        if output_path:
            self._save_figure(fig, output_path)
            plt.close(fig)
            return None
        else:
            return fig
    
    def _create_line_matrix(self, lines: np.ndarray, values: np.ndarray) -> np.ndarray:
        """Create a matrix for line-based heatmap visualization."""
        if len(lines) == 0:
            return np.array([[0]])
        
        # Create a simple matrix where each row represents a line
        # and columns represent temporal progression
        matrix = np.zeros((len(lines), max(10, len(lines))))
        
        for i, value in enumerate(values):
            # Spread the value across the row with some variation
            row_values = np.full(matrix.shape[1], value)
            # Add some temporal variation
            if value > 0:
                variation = np.random.normal(0, value * 0.1, matrix.shape[1])
                row_values = np.maximum(0, row_values + variation)
            matrix[i, :] = row_values
        
        return matrix
    
    def _save_figure(self, fig: plt.Figure, output_path: Path) -> None:
        """Save figure to file."""
        format = output_path.suffix[1:].lower()
        
        save_kwargs = {
            'dpi': self.dpi,
            'bbox_inches': 'tight',
            'pad_inches': 0.1,
        }
        
        if format == 'svg':
            save_kwargs['format'] = 'svg'
        elif format == 'pdf':
            save_kwargs['format'] = 'pdf'
        else:
            save_kwargs['format'] = 'png'
        
        fig.savefig(output_path, **save_kwargs)
    
    def create_activity_timeline(self, heatmap_data: Dict[str, VariableHeatmapData], 
                                output_path: Optional[Path] = None) -> Optional[plt.Figure]:
        """Create a timeline visualization of variable activity."""
        if not heatmap_data:
            raise ValueError("No heatmap data provided")
        
        fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(heatmap_data)))
        
        for i, (var_name, data) in enumerate(heatmap_data.items()):
            # Create timeline data
            x_data = data.line_numbers
            y_data = [data.access_counts[j] + data.modification_counts[j] * 2 
                     for j in range(len(data.line_numbers))]
            
            # Plot line
            ax.plot(x_data, y_data, marker='o', label=var_name, 
                   color=colors[i], linewidth=2, markersize=6)
        
        ax.set_xlabel('Line Number', fontsize=self.font_size)
        ax.set_ylabel('Activity Level', fontsize=self.font_size)
        ax.set_title('Variable Activity Timeline', fontsize=self.font_size + 4, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save or return
        if output_path:
            self._save_figure(fig, output_path)
            plt.close(fig)
            return None
        else:
            return fig
