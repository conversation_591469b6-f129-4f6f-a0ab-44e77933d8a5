#!/usr/bin/env python3
"""
FlowLens Web Development Server Startup Script

This script starts the FlowLens web application in development mode
with hot reloading and debug features enabled.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import fastapi
        import uvicorn
        import jinja2
        print("✓ Web dependencies found")
    except ImportError as e:
        print(f"✗ Missing web dependency: {e}")
        print("Please install with: pip install -r flowlens_web/requirements.txt")
        return False
    
    try:
        import flowlens
        print("✓ FlowLens core package found")
    except ImportError:
        print("✗ FlowLens core package not found")
        print("Please install with: pip install -e .")
        return False
    
    return True

def setup_environment():
    """Setup environment variables for development."""
    os.environ.setdefault("ENVIRONMENT", "development")
    os.environ.setdefault("LOG_LEVEL", "debug")
    os.environ.setdefault("RELOAD", "true")

def start_server():
    """Start the development server."""
    print("🚀 Starting FlowLens Web Development Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API documentation at: http://localhost:8000/api/docs")
    print("🔄 Hot reloading enabled")
    print("\nPress Ctrl+C to stop the server\n")
    
    try:
        # Change to the parent directory so imports work correctly
        parent_dir = Path(__file__).parent.parent
        os.chdir(parent_dir)
        
        # Start uvicorn with development settings
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "flowlens_web.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--reload-dir", "flowlens_web",
            "--reload-dir", "flowlens",
            "--log-level", "info"
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return 1
    
    return 0

def main():
    """Main entry point."""
    print("🔍 FlowLens Web Development Server")
    print("=" * 40)
    
    if not check_dependencies():
        return 1
    
    setup_environment()
    return start_server()

if __name__ == "__main__":
    sys.exit(main())
