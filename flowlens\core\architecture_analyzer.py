"""
FlowLens System Architecture Analyzer

This module analyzes entire codebases to understand system architecture,
module dependencies, and high-level code organization.

Features:
- Multi-file analysis and dependency mapping
- Module relationship discovery
- Class hierarchy analysis
- Function call graph generation
- Import dependency tracking
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque

from .ast_parser import ASTParser, ParseResult
from .analytics import track_feature_usage, track_operation


@dataclass
class ModuleInfo:
    """Information about a single module."""
    
    filepath: Path
    module_name: str
    parse_result: ParseResult
    imports: Set[str] = field(default_factory=set)
    exports: Set[str] = field(default_factory=set)
    classes: List[str] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    dependencies: Set[str] = field(default_factory=set)
    dependents: Set[str] = field(default_factory=set)


@dataclass
class ArchitectureAnalysis:
    """Complete architecture analysis results."""
    
    modules: Dict[str, ModuleInfo]
    dependency_graph: Dict[str, Set[str]]
    class_hierarchy: Dict[str, List[str]]
    function_calls: Dict[str, Set[str]]
    circular_dependencies: List[List[str]]
    entry_points: List[str]
    leaf_modules: List[str]
    complexity_metrics: Dict[str, Any]


class ArchitectureAnalyzer:
    """
    Analyzes system architecture across multiple Python files.
    
    Provides comprehensive analysis of code organization, dependencies,
    and architectural patterns in Python projects.
    """
    
    def __init__(self):
        self.ast_parser = ASTParser()
        self.modules: Dict[str, ModuleInfo] = {}
        self.dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        
    def analyze_directory(self, directory: Path, 
                         include_patterns: Optional[List[str]] = None,
                         exclude_patterns: Optional[List[str]] = None) -> ArchitectureAnalysis:
        """
        Analyze all Python files in a directory.
        
        Args:
            directory: Directory to analyze
            include_patterns: File patterns to include (e.g., ['*.py'])
            exclude_patterns: File patterns to exclude (e.g., ['test_*.py'])
            
        Returns:
            Complete architecture analysis
        """
        track_feature_usage('architecture_analysis_started', {
            'directory': str(directory),
            'has_include_patterns': include_patterns is not None,
            'has_exclude_patterns': exclude_patterns is not None
        })
        
        with track_operation("architecture_analysis"):
            # Find Python files
            python_files = self._find_python_files(directory, include_patterns, exclude_patterns)
            
            # Parse all files
            self._parse_files(python_files)
            
            # Build dependency graph
            self._build_dependency_graph()
            
            # Analyze architecture
            analysis = self._create_architecture_analysis()
            
            track_feature_usage('architecture_analysis_completed', {
                'modules_analyzed': len(self.modules),
                'dependencies_found': sum(len(deps) for deps in self.dependency_graph.values()),
                'circular_dependencies': len(analysis.circular_dependencies)
            })
            
            return analysis
    
    def analyze_files(self, filepaths: List[Path]) -> ArchitectureAnalysis:
        """
        Analyze specific Python files.
        
        Args:
            filepaths: List of Python files to analyze
            
        Returns:
            Complete architecture analysis
        """
        track_feature_usage('architecture_files_analysis_started', {
            'file_count': len(filepaths)
        })
        
        with track_operation("architecture_files_analysis"):
            # Parse all files
            self._parse_files(filepaths)
            
            # Build dependency graph
            self._build_dependency_graph()
            
            # Analyze architecture
            analysis = self._create_architecture_analysis()
            
            track_feature_usage('architecture_files_analysis_completed', {
                'modules_analyzed': len(self.modules),
                'dependencies_found': sum(len(deps) for deps in self.dependency_graph.values())
            })
            
            return analysis
    
    def _find_python_files(self, directory: Path, 
                          include_patterns: Optional[List[str]] = None,
                          exclude_patterns: Optional[List[str]] = None) -> List[Path]:
        """Find Python files in directory."""
        import fnmatch
        
        if include_patterns is None:
            include_patterns = ['*.py']
        
        if exclude_patterns is None:
            exclude_patterns = ['__pycache__/*', '*.pyc', 'test_*.py', '*_test.py']
        
        python_files = []
        
        for root, dirs, files in os.walk(directory):
            # Skip hidden directories and __pycache__
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
            
            for file in files:
                filepath = Path(root) / file
                
                # Check include patterns
                included = any(fnmatch.fnmatch(file, pattern) for pattern in include_patterns)
                if not included:
                    continue
                
                # Check exclude patterns
                excluded = any(fnmatch.fnmatch(str(filepath), pattern) for pattern in exclude_patterns)
                if excluded:
                    continue
                
                python_files.append(filepath)
        
        return python_files
    
    def _parse_files(self, filepaths: List[Path]) -> None:
        """Parse all Python files."""
        for filepath in filepaths:
            try:
                # Parse the file
                parse_result = self.ast_parser.parse_file(filepath)
                
                # Create module info
                module_name = self._get_module_name(filepath)
                module_info = ModuleInfo(
                    filepath=filepath,
                    module_name=module_name,
                    parse_result=parse_result
                )
                
                # Extract module information
                self._extract_module_info(module_info)
                
                # Store module
                self.modules[module_name] = module_info
                
            except Exception as e:
                # Log error but continue with other files
                track_feature_usage('architecture_parse_error', {
                    'filepath': str(filepath),
                    'error': str(e)
                })
                continue
    
    def _get_module_name(self, filepath: Path) -> str:
        """Get module name from filepath."""
        # Convert file path to module name
        parts = filepath.with_suffix('').parts
        
        # Remove common prefixes like 'src', 'lib', etc.
        start_idx = 0
        for i, part in enumerate(parts):
            if part in ['src', 'lib', 'app', 'source']:
                start_idx = i + 1
                break
        
        module_parts = parts[start_idx:]
        return '.'.join(module_parts)
    
    def _extract_module_info(self, module_info: ModuleInfo) -> None:
        """Extract detailed information from a module."""
        parse_result = module_info.parse_result
        
        # Extract imports
        for import_info in parse_result.imports:
            if import_info['type'] == 'import':
                module_info.imports.add(import_info['module'])
            elif import_info['type'] == 'from_import':
                module_info.imports.add(import_info['module'])
        
        # Extract classes
        for class_info in parse_result.classes:
            class_name = class_info['name']
            module_info.classes.append(class_name)
            module_info.exports.add(class_name)
        
        # Extract functions
        for func_info in parse_result.functions:
            func_name = func_info['name']
            if not func_name.startswith('_'):  # Only public functions
                module_info.functions.append(func_name)
                module_info.exports.add(func_name)
    
    def _build_dependency_graph(self) -> None:
        """Build module dependency graph."""
        for module_name, module_info in self.modules.items():
            for imported_module in module_info.imports:
                # Check if imported module is in our analyzed modules
                if imported_module in self.modules:
                    self.dependency_graph[module_name].add(imported_module)
                    module_info.dependencies.add(imported_module)
                    self.modules[imported_module].dependents.add(module_name)
    
    def _create_architecture_analysis(self) -> ArchitectureAnalysis:
        """Create complete architecture analysis."""
        return ArchitectureAnalysis(
            modules=self.modules.copy(),
            dependency_graph=dict(self.dependency_graph),
            class_hierarchy=self._analyze_class_hierarchy(),
            function_calls=self._analyze_function_calls(),
            circular_dependencies=self._find_circular_dependencies(),
            entry_points=self._find_entry_points(),
            leaf_modules=self._find_leaf_modules(),
            complexity_metrics=self._calculate_complexity_metrics()
        )
    
    def _analyze_class_hierarchy(self) -> Dict[str, List[str]]:
        """Analyze class inheritance hierarchy."""
        hierarchy = defaultdict(list)
        
        for module_info in self.modules.values():
            for class_info in module_info.parse_result.classes:
                class_name = class_info['name']
                bases = class_info.get('bases', [])
                
                for base in bases:
                    hierarchy[base].append(f"{module_info.module_name}.{class_name}")
        
        return dict(hierarchy)
    
    def _analyze_function_calls(self) -> Dict[str, Set[str]]:
        """Analyze function call relationships."""
        calls = defaultdict(set)
        
        # This is a simplified implementation
        # In a full implementation, you'd analyze the AST for function calls
        for module_info in self.modules.values():
            module_name = module_info.module_name
            
            # Add calls based on imports (simplified)
            for imported_module in module_info.imports:
                if imported_module in self.modules:
                    calls[module_name].add(imported_module)
        
        return dict(calls)
    
    def _find_circular_dependencies(self) -> List[List[str]]:
        """Find circular dependencies in the module graph."""
        def dfs(node: str, path: List[str], visited: Set[str]) -> List[List[str]]:
            if node in path:
                # Found a cycle
                cycle_start = path.index(node)
                return [path[cycle_start:] + [node]]
            
            if node in visited:
                return []
            
            visited.add(node)
            path.append(node)
            
            cycles = []
            for neighbor in self.dependency_graph.get(node, []):
                cycles.extend(dfs(neighbor, path.copy(), visited))
            
            return cycles
        
        all_cycles = []
        visited = set()
        
        for module in self.modules:
            if module not in visited:
                cycles = dfs(module, [], visited)
                all_cycles.extend(cycles)
        
        # Remove duplicates
        unique_cycles = []
        for cycle in all_cycles:
            if cycle not in unique_cycles:
                unique_cycles.append(cycle)
        
        return unique_cycles
    
    def _find_entry_points(self) -> List[str]:
        """Find modules that are likely entry points (no dependents)."""
        entry_points = []
        
        for module_name, module_info in self.modules.items():
            # Check if module has __main__ block or no dependents
            has_main = any('__main__' in line for line in module_info.parse_result.source_code.splitlines())
            has_no_dependents = len(module_info.dependents) == 0
            
            if has_main or (has_no_dependents and module_info.functions):
                entry_points.append(module_name)
        
        return entry_points
    
    def _find_leaf_modules(self) -> List[str]:
        """Find modules that don't depend on other analyzed modules."""
        leaf_modules = []
        
        for module_name, module_info in self.modules.items():
            if len(module_info.dependencies) == 0:
                leaf_modules.append(module_name)
        
        return leaf_modules
    
    def _calculate_complexity_metrics(self) -> Dict[str, Any]:
        """Calculate overall architecture complexity metrics."""
        total_modules = len(self.modules)
        total_dependencies = sum(len(deps) for deps in self.dependency_graph.values())
        
        # Calculate average dependencies per module
        avg_dependencies = total_dependencies / total_modules if total_modules > 0 else 0
        
        # Calculate coupling metrics
        max_dependencies = max((len(deps) for deps in self.dependency_graph.values()), default=0)
        max_dependents = max((len(module.dependents) for module in self.modules.values()), default=0)
        
        # Calculate total lines of code
        total_loc = sum(module.parse_result.total_lines for module in self.modules.values())
        
        # Calculate total complexity
        total_complexity = sum(module.parse_result.complexity_score for module in self.modules.values())
        
        return {
            'total_modules': total_modules,
            'total_dependencies': total_dependencies,
            'average_dependencies_per_module': avg_dependencies,
            'max_dependencies': max_dependencies,
            'max_dependents': max_dependents,
            'total_lines_of_code': total_loc,
            'total_complexity_score': total_complexity,
            'average_complexity_per_module': total_complexity / total_modules if total_modules > 0 else 0,
            'coupling_factor': total_dependencies / (total_modules * (total_modules - 1)) if total_modules > 1 else 0
        }
