#!/usr/bin/env python3
"""
Test script to demonstrate FlowLens enhanced visualization capabilities.

This script tests the new Mermaid diagrams, architecture analysis,
and improved flow diagrams to showcase the enhanced visualization features.
"""

import os
import tempfile
from pathlib import Path

# Set environment to free tier for testing
os.environ['FLOWLENS_TIER'] = 'free'
os.environ['FLOWLENS_ANALYTICS'] = 'true'

def create_sample_code_files():
    """Create sample Python files for testing."""
    # Create a temporary directory
    temp_dir = Path(tempfile.mkdtemp(prefix="flowlens_test_"))
    
    # Sample module 1: Calculator
    calculator_code = '''
"""Calculator module with basic operations."""

class Calculator:
    """A simple calculator class."""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        """Add two numbers."""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        """Multiply two numbers."""
        if a == 0 or b == 0:
            return 0
        
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def get_history(self):
        """Get calculation history."""
        return self.history.copy()

def factorial(n):
    """Calculate factorial recursively."""
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)
    '''
    
    # Sample module 2: Utils
    utils_code = '''
"""Utility functions."""

from .calculator import Calculator

def is_prime(n):
    """Check if a number is prime."""
    if n < 2:
        return False
    
    for i in range(2, int(n ** 0.5) + 1):
        if n % i == 0:
            return False
    
    return True

def fibonacci_sequence(n):
    """Generate Fibonacci sequence."""
    sequence = []
    a, b = 0, 1
    
    for _ in range(n):
        sequence.append(a)
        a, b = b, a + b
    
    return sequence

class MathUtils:
    """Mathematical utility class."""
    
    @staticmethod
    def gcd(a, b):
        """Calculate greatest common divisor."""
        while b:
            a, b = b, a % b
        return a
    
    @staticmethod
    def lcm(a, b):
        """Calculate least common multiple."""
        return abs(a * b) // MathUtils.gcd(a, b)
    '''
    
    # Sample module 3: Main
    main_code = '''
"""Main application module."""

from .calculator import Calculator, factorial
from .utils import is_prime, fibonacci_sequence, MathUtils

def main():
    """Main application function."""
    print("FlowLens Visualization Demo")
    
    # Test calculator
    calc = Calculator()
    result1 = calc.add(10, 5)
    result2 = calc.multiply(3, 4)
    
    print(f"Addition result: {result1}")
    print(f"Multiplication result: {result2}")
    
    # Test factorial
    fact_result = factorial(5)
    print(f"Factorial of 5: {fact_result}")
    
    # Test prime checking
    numbers = [2, 3, 4, 5, 6, 7, 8, 9, 10]
    primes = [n for n in numbers if is_prime(n)]
    print(f"Prime numbers: {primes}")
    
    # Test Fibonacci
    fib_seq = fibonacci_sequence(10)
    print(f"Fibonacci sequence: {fib_seq}")
    
    # Test math utils
    gcd_result = MathUtils.gcd(48, 18)
    lcm_result = MathUtils.lcm(12, 8)
    print(f"GCD(48, 18): {gcd_result}")
    print(f"LCM(12, 8): {lcm_result}")

if __name__ == "__main__":
    main()
    '''
    
    # Create package structure
    package_dir = temp_dir / "sample_package"
    package_dir.mkdir()
    
    # Write files
    (package_dir / "__init__.py").write_text("")
    (package_dir / "calculator.py").write_text(calculator_code)
    (package_dir / "utils.py").write_text(utils_code)
    (package_dir / "main.py").write_text(main_code)
    
    return temp_dir, package_dir

def test_mermaid_diagrams():
    """Test Mermaid diagram generation."""
    print("🎨 Testing Mermaid Diagram Generation")
    print("=" * 50)
    
    temp_dir, package_dir = create_sample_code_files()
    
    try:
        from flowlens.visualization.mermaid_renderer import MermaidRenderer
        from flowlens.core.ast_parser import ASTParser
        from flowlens.core.cfg_generator import CFGGenerator
        
        # Test flowchart generation
        print("📊 Testing flowchart generation...")
        
        renderer = MermaidRenderer()
        parser = ASTParser()
        cfg_generator = CFGGenerator()
        
        # Parse calculator file
        calc_file = package_dir / "calculator.py"
        parse_result = parser.parse_file(calc_file)
        
        # Generate CFG for multiply method
        cfg = cfg_generator.generate_cfg(parse_result, "multiply")
        
        # Generate Mermaid flowchart
        flowchart_content = renderer.render_flowchart(cfg, "Calculator.multiply() Method")
        
        print("✅ Flowchart generated successfully!")
        print(f"   Content length: {len(flowchart_content)} characters")
        print(f"   Lines: {len(flowchart_content.splitlines())}")
        
        # Test class diagram generation
        print("\n🏗️  Testing class diagram generation...")
        
        class_diagram_content = renderer.render_class_diagram(parse_result, "Calculator Module")
        
        print("✅ Class diagram generated successfully!")
        print(f"   Content length: {len(class_diagram_content)} characters")
        print(f"   Classes found: {len(parse_result.classes)}")
        
        # Save examples
        output_dir = Path("visualization_examples")
        output_dir.mkdir(exist_ok=True)
        
        (output_dir / "calculator_flowchart.mmd").write_text(flowchart_content)
        (output_dir / "calculator_classes.mmd").write_text(class_diagram_content)
        
        print(f"\n💾 Examples saved to: {output_dir}")
        
    except Exception as e:
        print(f"❌ Mermaid test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

def test_architecture_analysis():
    """Test system architecture analysis."""
    print("\n🏛️  Testing Architecture Analysis")
    print("=" * 50)
    
    temp_dir, package_dir = create_sample_code_files()
    
    try:
        from flowlens.core.architecture_analyzer import ArchitectureAnalyzer
        from flowlens.visualization.mermaid_renderer import MermaidRenderer
        
        # Analyze the sample package
        analyzer = ArchitectureAnalyzer()
        analysis = analyzer.analyze_directory(package_dir)
        
        print(f"📈 Architecture Analysis Results:")
        print(f"   Modules analyzed: {len(analysis.modules)}")
        print(f"   Total dependencies: {sum(len(deps) for deps in analysis.dependency_graph.values())}")
        print(f"   Circular dependencies: {len(analysis.circular_dependencies)}")
        print(f"   Entry points: {len(analysis.entry_points)}")
        print(f"   Total lines of code: {analysis.complexity_metrics['total_lines_of_code']}")
        
        # Generate architecture diagram
        renderer = MermaidRenderer()
        parse_results = [module.parse_result for module in analysis.modules.values()]
        arch_diagram = renderer.render_architecture_overview(parse_results, "Sample Package Architecture")
        
        print("\n✅ Architecture diagram generated!")
        print(f"   Diagram length: {len(arch_diagram)} characters")
        
        # Save architecture diagram
        output_dir = Path("visualization_examples")
        output_dir.mkdir(exist_ok=True)
        (output_dir / "package_architecture.mmd").write_text(arch_diagram)
        
        # Display module details
        print(f"\n📋 Module Details:")
        for module_name, module_info in analysis.modules.items():
            print(f"   {module_name}:")
            print(f"     Classes: {len(module_info.classes)}")
            print(f"     Functions: {len(module_info.functions)}")
            print(f"     Dependencies: {len(module_info.dependencies)}")
        
    except Exception as e:
        print(f"❌ Architecture test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

def test_improved_flow_diagrams():
    """Test improved flow diagram rendering."""
    print("\n🎯 Testing Improved Flow Diagrams")
    print("=" * 50)
    
    try:
        from flowlens.visualization.flow_diagrams import FlowDiagramRenderer
        from flowlens.core.ast_parser import ASTParser
        from flowlens.core.cfg_generator import CFGGenerator
        
        # Create a test file with complex control flow
        complex_code = '''
def complex_algorithm(data, threshold=10):
    """A complex algorithm with multiple control structures."""
    result = []
    
    for item in data:
        if item > threshold:
            # Process large items
            processed = item * 2
            
            if processed > 50:
                result.append(processed)
            else:
                # Apply additional processing
                for i in range(3):
                    processed += i
                result.append(processed)
        
        elif item < 0:
            # Handle negative items
            try:
                normalized = abs(item)
                result.append(normalized)
            except Exception as e:
                print(f"Error: {e}")
                continue
        
        else:
            # Handle normal items
            result.append(item)
    
    return result
        '''
        
        # Parse and generate CFG
        parser = ASTParser()
        cfg_generator = CFGGenerator()
        
        parse_result = parser.parse_code(complex_code, "complex_example.py")
        cfg = cfg_generator.generate_cfg(parse_result, "complex_algorithm")
        
        print(f"📊 CFG Analysis:")
        print(f"   Total nodes: {len(cfg.nodes)}")
        print(f"   Total edges: {len(cfg.edges)}")
        print(f"   Decision points: {len([n for n in cfg.nodes.values() if n.node_type.value == 'condition'])}")
        
        # Test improved renderer
        renderer = FlowDiagramRenderer()
        
        # Save improved diagram
        output_dir = Path("visualization_examples")
        output_dir.mkdir(exist_ok=True)
        
        output_path = output_dir / "improved_flow_diagram.png"
        renderer.render_to_file(cfg, output_path, "png")
        
        print(f"✅ Improved flow diagram saved to: {output_path}")
        print(f"   File size: {output_path.stat().st_size / 1024:.1f} KB")
        
    except Exception as e:
        print(f"❌ Flow diagram test failed: {e}")
        import traceback
        traceback.print_exc()

def test_cli_integration():
    """Test CLI integration with new commands."""
    print("\n💻 Testing CLI Integration")
    print("=" * 50)
    
    temp_dir, package_dir = create_sample_code_files()
    
    try:
        import subprocess
        import sys
        
        # Test mermaid command
        print("🔧 Testing mermaid CLI command...")
        
        calc_file = package_dir / "calculator.py"
        result = subprocess.run([
            sys.executable, "-m", "flowlens.cli.main", "mermaid", 
            str(calc_file), "--type", "flowchart", "--output", "test_cli.mmd"
        ], capture_output=True, text=True, cwd=Path.cwd())
        
        if result.returncode == 0:
            print("✅ Mermaid CLI command successful!")
            if Path("test_cli.mmd").exists():
                print(f"   Output file created: {Path('test_cli.mmd').stat().st_size} bytes")
        else:
            print(f"❌ Mermaid CLI command failed: {result.stderr}")
        
        # Test architecture command
        print("\n🏗️  Testing architecture CLI command...")
        
        result = subprocess.run([
            sys.executable, "-m", "flowlens.cli.main", "architecture", 
            str(package_dir), "--output", "test_arch.mmd"
        ], capture_output=True, text=True, cwd=Path.cwd())
        
        if result.returncode == 0:
            print("✅ Architecture CLI command successful!")
            if Path("test_arch.mmd").exists():
                print(f"   Output file created: {Path('test_arch.mmd').stat().st_size} bytes")
        else:
            print(f"❌ Architecture CLI command failed: {result.stderr}")
        
    except Exception as e:
        print(f"❌ CLI integration test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        # Clean up test files
        for file in ["test_cli.mmd", "test_arch.mmd"]:
            if Path(file).exists():
                Path(file).unlink()

def main():
    """Run all visualization tests."""
    print("🎨 FlowLens Enhanced Visualization Test Suite")
    print("=" * 60)
    
    try:
        test_mermaid_diagrams()
        test_architecture_analysis()
        test_improved_flow_diagrams()
        test_cli_integration()
        
        print("\n✅ All visualization tests completed!")
        
        # Show final summary
        output_dir = Path("visualization_examples")
        if output_dir.exists():
            files = list(output_dir.glob("*"))
            print(f"\n📁 Generated {len(files)} example files in {output_dir}:")
            for file in files:
                print(f"   📄 {file.name} ({file.stat().st_size} bytes)")
        
        print("\n🎯 Key Features Demonstrated:")
        print("   ✓ Mermaid flowchart generation")
        print("   ✓ Mermaid class diagram generation")
        print("   ✓ System architecture analysis")
        print("   ✓ Improved flow diagram rendering")
        print("   ✓ CLI integration for all features")
        print("   ✓ Freemium tier restrictions")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
