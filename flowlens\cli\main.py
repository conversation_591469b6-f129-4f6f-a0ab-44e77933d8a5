"""
FlowLens Main CLI Entry Point

This module provides the main command-line interface for FlowLens,
handling command parsing, execution, and user interaction.

Features:
- Click-based CLI framework
- Command routing and execution
- Error handling and user feedback
- Performance monitoring integration
"""

import sys
import click
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.traceback import install as install_rich_traceback

from ..core import <PERSON>TParser, CFGGenerator, ExecutionTracker, VariableMonitor
from ..utils.performance import PerformanceMonitor, get_global_monitor
from ..utils.validators import InputValidator, CodeValidator
try:
    from .commands import (analyze_command, step_command, graph_command, heatmap_command,
                      repl_command, display_tier_info, mermaid_command, architecture_command)
except ImportError:
    # Handle missing dependencies during installation
    def analyze_command(*args, **kwargs):
        print("FlowLens not fully installed. Please run: pip install flowlens")
        return 1

    def step_command(*args, **kwargs):
        print("FlowLens not fully installed. Please run: pip install flowlens")
        return 1

    def graph_command(*args, **kwargs):
        print("FlowLens not fully installed. Please run: pip install flowlens")
        return 1

    def heatmap_command(*args, **kwargs):
        print("Flow<PERSON>ens not fully installed. Please run: pip install flowlens")
        return 1

    def repl_command(*args, **kwargs):
        print("FlowLens not fully installed. Please run: pip install flowlens")
        return 1
from .interactive import InteractiveSession

# Install rich traceback for better error display
install_rich_traceback()

# Global console for rich output
console = Console()


@click.group(invoke_without_command=True)
@click.option('--version', is_flag=True, help='Show version information')
@click.option('--performance', is_flag=True, help='Enable performance monitoring')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.pass_context
def cli(ctx, version, performance, verbose):
    """
    FlowLens - Interactive Python Code Visualization & Execution Analysis
    
    A powerful CLI tool for analyzing Python code execution flow,
    generating control flow graphs, and tracking variable states.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Store global options
    ctx.obj['verbose'] = verbose
    ctx.obj['performance'] = performance
    
    if version:
        from .. import __version__, __author__
        console.print(f"FlowLens version {__version__}")
        console.print(f"Author: {__author__}")
        return
    
    # If no command specified, show help
    if ctx.invoked_subcommand is None:
        console.print(ctx.get_help())
        return


@cli.command()
@click.argument('filepath', type=click.Path(exists=True, path_type=Path))
@click.option('--function', '-f', help='Specific function to analyze')
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output file path')
@click.option('--format', 'output_format', type=click.Choice(['json', 'yaml', 'text']), 
              default='text', help='Output format')
@click.option('--performance', is_flag=True, help='Include performance metrics')
@click.pass_context
def analyze(ctx, filepath, function, output, output_format, performance):
    """Analyze Python code and generate comprehensive report."""
    return analyze_command(ctx, filepath, function, output, output_format, performance)


@cli.command()
@click.argument('filepath', type=click.Path(exists=True, path_type=Path))
@click.option('--function', '-f', help='Specific function to step through')
@click.option('--max-steps', type=int, default=1000, help='Maximum number of steps')
@click.option('--auto-run', is_flag=True, help='Run automatically without user input')
@click.pass_context
def step(ctx, filepath, function, max_steps, auto_run):
    """Interactive step-through execution of Python code."""
    return step_command(ctx, filepath, function, max_steps, auto_run)


@cli.command()
@click.argument('filepath', type=click.Path(exists=True, path_type=Path))
@click.option('--function', '-f', help='Specific function to visualize')
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output image file')
@click.option('--format', 'output_format', type=click.Choice(['png', 'svg', 'pdf']), 
              default='png', help='Output image format')
@click.option('--theme', type=click.Choice(['default', 'dark', 'light', 'colorful']), 
              default='default', help='Visualization theme')
@click.option('--layout', type=click.Choice(['hierarchical', 'circular', 'spring']), 
              default='hierarchical', help='Graph layout algorithm')
@click.pass_context
def graph(ctx, filepath, function, output, output_format, theme, layout):
    """Generate control flow graph visualization."""
    return graph_command(ctx, filepath, function, output, output_format, theme, layout)


@cli.command()
@click.argument('filepath', type=click.Path(exists=True, path_type=Path))
@click.option('--function', '-f', help='Specific function to analyze')
@click.option('--variable', '-var', help='Specific variable to focus on')
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output image file')
@click.option('--format', 'output_format', type=click.Choice(['png', 'svg', 'pdf']), 
              default='png', help='Output image format')
@click.option('--colormap', type=str, default='viridis', help='Matplotlib colormap name')
@click.pass_context
def heatmap(ctx, filepath, function, variable, output, output_format, colormap):
    """Generate variable state heatmap visualization."""
    return heatmap_command(ctx, filepath, function, variable, output, output_format, colormap)


@cli.command()
@click.option('--history-size', type=int, default=1000, help='Command history size')
@click.pass_context
def repl(ctx, history_size):
    """Start interactive REPL mode for code analysis."""
    return repl_command(ctx, history_size)


@cli.command()
@click.argument('filepath', type=click.Path(exists=True, path_type=Path))
@click.option('--function', '-f', help='Specific function to analyze')
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output file path')
@click.option('--format', 'output_format', type=click.Choice(['mmd', 'html', 'png', 'svg', 'pdf']),
              default='mmd', help='Output format')
@click.option('--type', 'diagram_type', type=click.Choice(['flowchart', 'class']),
              default='flowchart', help='Diagram type')
@click.option('--theme', type=click.Choice(['default', 'dark', 'forest', 'base', 'neutral']),
              default='default', help='Mermaid theme')
@click.option('--direction', type=click.Choice(['TD', 'TB', 'BT', 'RL', 'LR']),
              default='TD', help='Diagram direction')
@click.pass_context
def mermaid(ctx, filepath, function, output, output_format, diagram_type, theme, direction):
    """Generate Mermaid diagram for code visualization."""
    return mermaid_command(ctx, filepath, function, output, output_format, diagram_type, theme, direction)


@cli.command()
@click.argument('directory', type=click.Path(exists=True, file_okay=False, path_type=Path))
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output diagram file')
@click.option('--format', 'output_format', type=click.Choice(['mmd', 'html', 'png', 'svg', 'pdf']),
              default='mmd', help='Output format')
@click.option('--include', 'include_patterns', help='Include file patterns (comma-separated)')
@click.option('--exclude', 'exclude_patterns', help='Exclude file patterns (comma-separated)')
@click.pass_context
def architecture(ctx, directory, output, output_format, include_patterns, exclude_patterns):
    """Analyze and visualize system architecture."""
    return architecture_command(ctx, directory, output, output_format, include_patterns, exclude_patterns)


@cli.command()
@click.option('--pricing', is_flag=True, help='Show pricing information')
@click.pass_context
def tier(ctx, pricing):
    """Show current tier information and pricing."""
    from ..core.licensing import get_license_manager

    if pricing:
        # Show pricing information
        license_manager = get_license_manager()
        pricing_info = license_manager.get_pricing_info()

        from rich.table import Table

        table = Table(title="FlowLens Pricing Tiers")
        table.add_column("Tier", style="cyan")
        table.add_column("Price", style="green")
        table.add_column("Key Features", style="yellow")

        for tier_name, tier_data in pricing_info['tiers'].items():
            features_text = "\n".join(f"• {feature}" for feature in tier_data['features'][:3])
            if len(tier_data['features']) > 3:
                features_text += f"\n• ... and {len(tier_data['features']) - 3} more"

            table.add_row(
                tier_data['name'],
                tier_data['price'],
                features_text
            )

        console.print(table)
        console.print("\n[blue]Visit https://flowlens.com/pricing for complete details[/blue]")
    else:
        # Show current tier info
        display_tier_info()


@cli.command()
@click.argument('filepath', type=click.Path(exists=True, path_type=Path))
@click.option('--function', '-f', help='Specific function to analyze')
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output report file')
@click.option('--format', 'output_format', type=click.Choice(['json', 'html', 'pdf']), 
              default='html', help='Report format')
@click.pass_context
def report(ctx, filepath, function, output, output_format):
    """Generate comprehensive analysis report."""
    try:
        # Initialize components
        validator = InputValidator()
        code_validator = CodeValidator()
        parser = ASTParser()
        cfg_generator = CFGGenerator()
        tracker = ExecutionTracker()
        monitor = VariableMonitor()
        
        # Validate input
        path_result = validator.validate_file_path(filepath)
        if not path_result.is_valid:
            console.print(f"[red]Error:[/red] {'; '.join(path_result.errors)}")
            return 1
        
        # Load and validate code
        with open(filepath, 'r') as f:
            code = f.read()
        
        code_result = code_validator.validate_code_string(code)
        if not code_result.is_valid:
            console.print(f"[red]Code validation failed:[/red] {'; '.join(code_result.errors)}")
            return 1
        
        if code_result.warnings:
            console.print(f"[yellow]Warnings:[/yellow] {'; '.join(code_result.warnings)}")
        
        # Parse code
        console.print("Parsing code...")
        parse_result = parser.parse_code(code, str(filepath))
        
        # Generate CFG
        console.print("Generating control flow graph...")
        cfg = cfg_generator.generate_cfg(parse_result, function)
        
        # Execute and track
        console.print("Executing and tracking...")
        execution_id = tracker.start_tracking(code, cfg)
        execution_result = tracker.run_to_completion(execution_id)
        
        # Monitor variables
        monitor.start_monitoring()
        variable_analysis = monitor.process_execution_result(execution_result)
        
        # Generate report
        report_data = {
            "filepath": str(filepath),
            "function": function,
            "parse_summary": parse_result.summary,
            "cfg_metrics": cfg.get_complexity_metrics(),
            "execution_summary": {
                "duration_ms": execution_result.duration_ms,
                "total_steps": execution_result.total_steps,
                "final_state": execution_result.final_state.value,
            },
            "variable_analysis": variable_analysis,
            "timestamp": execution_result.start_time,
        }
        
        # Output report
        if output:
            if output_format == 'json':
                import json
                with open(output, 'w') as f:
                    json.dump(report_data, f, indent=2, default=str)
            elif output_format == 'html':
                # Generate HTML report (simplified)
                html_content = f"""
                <html>
                <head><title>FlowLens Analysis Report</title></head>
                <body>
                <h1>FlowLens Analysis Report</h1>
                <h2>File: {filepath}</h2>
                <pre>{json.dumps(report_data, indent=2, default=str)}</pre>
                </body>
                </html>
                """
                with open(output, 'w') as f:
                    f.write(html_content)
            
            console.print(f"[green]Report saved to:[/green] {output}")
        else:
            # Print to console
            import json
            console.print(json.dumps(report_data, indent=2, default=str))
        
        return 0
        
    except Exception as e:
        console.print(f"[red]Error generating report:[/red] {e}")
        if ctx.obj.get('verbose'):
            console.print_exception()
        return 1


def main():
    """Main entry point for the FlowLens CLI."""
    try:
        # Initialize performance monitoring if requested
        performance_monitor = get_global_monitor()
        
        # Run CLI
        exit_code = cli(standalone_mode=False)
        
        # Show performance report if monitoring was enabled
        if performance_monitor and hasattr(performance_monitor, '_metrics_history') and performance_monitor._metrics_history:
            console.print("\n[blue]Performance Report:[/blue]")
            report = performance_monitor.get_performance_report()
            
            for op_name, stats in report['operation_stats'].items():
                if stats:
                    console.print(f"  {op_name}: {stats['avg_duration_ms']:.2f}ms avg")
        
        sys.exit(exit_code or 0)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Unexpected error:[/red] {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
