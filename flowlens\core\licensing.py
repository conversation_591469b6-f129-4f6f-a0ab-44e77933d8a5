"""
FlowLens Licensing and Tier Management

This module manages user tiers, feature limitations, and upgrade prompts
for the freemium business model.

Features:
- Tier-based feature gating
- Usage limit enforcement
- Upgrade prompt generation
- License validation
- Feature availability checking
"""

import os
import json
import time
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass
from datetime import datetime, timedelta

from .analytics import track_conversion_event, track_upgrade_prompt


class UserTier(Enum):
    """User subscription tiers."""
    FREE = "free"
    PRO = "pro"
    TEAM = "team"
    ENTERPRISE = "enterprise"


@dataclass
class TierLimits:
    """Limits and features for each tier."""
    
    max_file_lines: Optional[int]
    max_files_per_session: Optional[int]
    export_formats: Set[str]
    advanced_features: Set[str]
    performance_profiling: bool
    collaboration_features: bool
    priority_support: bool
    custom_themes: bool
    
    @classmethod
    def get_tier_limits(cls, tier: UserTier) -> 'TierLimits':
        """Get limits for a specific tier."""
        limits_map = {
            UserTier.FREE: cls(
                max_file_lines=100,
                max_files_per_session=5,
                export_formats={'png'},
                advanced_features=set(),
                performance_profiling=False,
                collaboration_features=False,
                priority_support=False,
                custom_themes=False
            ),
            UserTier.PRO: cls(
                max_file_lines=None,  # Unlimited
                max_files_per_session=None,  # Unlimited
                export_formats={'png', 'svg', 'pdf', 'html'},
                advanced_features={'interactive_viz', 'animated_flow', 'custom_layouts'},
                performance_profiling=True,
                collaboration_features=False,
                priority_support=True,
                custom_themes=True
            ),
            UserTier.TEAM: cls(
                max_file_lines=None,
                max_files_per_session=None,
                export_formats={'png', 'svg', 'pdf', 'html'},
                advanced_features={'interactive_viz', 'animated_flow', 'custom_layouts', 'team_sharing'},
                performance_profiling=True,
                collaboration_features=True,
                priority_support=True,
                custom_themes=True
            ),
            UserTier.ENTERPRISE: cls(
                max_file_lines=None,
                max_files_per_session=None,
                export_formats={'png', 'svg', 'pdf', 'html'},
                advanced_features={'interactive_viz', 'animated_flow', 'custom_layouts', 'team_sharing', 'enterprise_analytics'},
                performance_profiling=True,
                collaboration_features=True,
                priority_support=True,
                custom_themes=True
            )
        }
        return limits_map[tier]


@dataclass
class UpgradePrompt:
    """Represents an upgrade prompt to show to users."""
    
    title: str
    message: str
    feature_blocked: str
    recommended_tier: UserTier
    call_to_action: str
    benefits: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for display."""
        return {
            'title': self.title,
            'message': self.message,
            'feature_blocked': self.feature_blocked,
            'recommended_tier': self.recommended_tier.value,
            'call_to_action': self.call_to_action,
            'benefits': self.benefits
        }


class LicenseManager:
    """
    Manages user licensing, tier enforcement, and upgrade prompts.
    
    Handles feature gating, usage limits, and conversion optimization
    for the freemium business model.
    """
    
    def __init__(self):
        self.current_tier = self._detect_user_tier()
        self.tier_limits = TierLimits.get_tier_limits(self.current_tier)
        self.session_usage = {
            'files_analyzed': 0,
            'exports_attempted': 0,
            'features_used': set(),
            'session_start': time.time()
        }
        self._upgrade_prompts_shown = set()
    
    def _detect_user_tier(self) -> UserTier:
        """Detect user tier from environment or license file."""
        # Check environment variable first
        tier_env = os.environ.get('FLOWLENS_TIER', 'free').lower()
        
        # Check for license file
        license_file = Path.home() / '.flowlens' / 'license.json'
        if license_file.exists():
            try:
                with open(license_file) as f:
                    license_data = json.load(f)
                    tier_from_file = license_data.get('tier', 'free').lower()
                    
                    # Validate license (simplified)
                    if self._validate_license(license_data):
                        return UserTier(tier_from_file)
            except (json.JSONDecodeError, ValueError, KeyError):
                pass
        
        # Default to environment or free
        try:
            return UserTier(tier_env)
        except ValueError:
            return UserTier.FREE
    
    def _validate_license(self, license_data: Dict[str, Any]) -> bool:
        """Validate license data (simplified implementation)."""
        required_fields = ['tier', 'user_id', 'expires_at']
        
        # Check required fields
        if not all(field in license_data for field in required_fields):
            return False
        
        # Check expiration
        expires_at = license_data.get('expires_at')
        if expires_at and expires_at < time.time():
            return False
        
        return True
    
    def can_analyze_file(self, file_lines: int) -> bool:
        """Check if user can analyze a file with given line count."""
        if self.tier_limits.max_file_lines is None:
            return True
        
        return file_lines <= self.tier_limits.max_file_lines
    
    def can_export_format(self, format: str) -> bool:
        """Check if user can export in given format."""
        return format.lower() in self.tier_limits.export_formats
    
    def can_use_feature(self, feature_name: str) -> bool:
        """Check if user can use advanced feature."""
        return feature_name in self.tier_limits.advanced_features
    
    def check_session_limits(self) -> bool:
        """Check if user has exceeded session limits."""
        if self.tier_limits.max_files_per_session is None:
            return True
        
        return self.session_usage['files_analyzed'] < self.tier_limits.max_files_per_session
    
    def record_file_analysis(self, file_lines: int) -> None:
        """Record that a file was analyzed."""
        self.session_usage['files_analyzed'] += 1
        
        # Track analytics
        track_conversion_event('file_analyzed', {
            'file_lines': file_lines,
            'user_tier': self.current_tier.value,
            'session_files': self.session_usage['files_analyzed']
        })
    
    def record_export_attempt(self, format: str, success: bool) -> None:
        """Record an export attempt."""
        self.session_usage['exports_attempted'] += 1
        
        # Track analytics
        track_conversion_event('export_attempted', {
            'format': format,
            'success': success,
            'user_tier': self.current_tier.value
        })
    
    def record_feature_usage(self, feature_name: str) -> None:
        """Record feature usage."""
        self.session_usage['features_used'].add(feature_name)
        
        # Track analytics
        track_conversion_event('feature_used', {
            'feature': feature_name,
            'user_tier': self.current_tier.value
        })
    
    def get_upgrade_prompt(self, blocked_feature: str, context: Dict[str, Any] = None) -> UpgradePrompt:
        """Generate appropriate upgrade prompt for blocked feature."""
        context = context or {}
        
        # File size limit prompt
        if blocked_feature == 'file_size_limit':
            file_lines = context.get('file_lines', 0)
            return UpgradePrompt(
                title="File Too Large for Free Tier",
                message=f"This file has {file_lines} lines, but the free tier is limited to {self.tier_limits.max_file_lines} lines.",
                feature_blocked="large_file_analysis",
                recommended_tier=UserTier.PRO,
                call_to_action="Upgrade to FlowLens Pro for unlimited file analysis",
                benefits=[
                    "Analyze files of any size",
                    "Advanced visualizations",
                    "All export formats",
                    "Performance profiling",
                    "Priority support"
                ]
            )
        
        # Export format prompt
        elif blocked_feature == 'export_format':
            format = context.get('format', 'unknown')
            return UpgradePrompt(
                title="Premium Export Format",
                message=f"{format.upper()} export is available in FlowLens Pro and higher tiers.",
                feature_blocked="premium_export",
                recommended_tier=UserTier.PRO,
                call_to_action="Upgrade to unlock all export formats",
                benefits=[
                    "Export to SVG, PDF, and HTML",
                    "High-quality vector graphics",
                    "Interactive visualizations",
                    "Professional presentations"
                ]
            )
        
        # Advanced features prompt
        elif blocked_feature == 'advanced_feature':
            feature = context.get('feature', 'unknown')
            return UpgradePrompt(
                title="Advanced Feature",
                message=f"{feature} is a premium feature available in FlowLens Pro.",
                feature_blocked="advanced_features",
                recommended_tier=UserTier.PRO,
                call_to_action="Upgrade for advanced visualization features",
                benefits=[
                    "Interactive 3D visualizations",
                    "Animated execution flow",
                    "Custom layouts and themes",
                    "Performance profiling"
                ]
            )
        
        # Session limit prompt
        elif blocked_feature == 'session_limit':
            return UpgradePrompt(
                title="Session Limit Reached",
                message=f"You've analyzed {self.tier_limits.max_files_per_session} files in this session. Free tier is limited to {self.tier_limits.max_files_per_session} files per session.",
                feature_blocked="session_limits",
                recommended_tier=UserTier.PRO,
                call_to_action="Upgrade for unlimited analysis",
                benefits=[
                    "Unlimited files per session",
                    "No daily limits",
                    "Batch processing",
                    "Project-wide analysis"
                ]
            )
        
        # Default prompt
        else:
            return UpgradePrompt(
                title="Premium Feature",
                message="This feature is available in FlowLens Pro and higher tiers.",
                feature_blocked=blocked_feature,
                recommended_tier=UserTier.PRO,
                call_to_action="Upgrade to FlowLens Pro",
                benefits=[
                    "Unlimited code analysis",
                    "All export formats",
                    "Advanced features",
                    "Priority support"
                ]
            )
    
    def show_upgrade_prompt(self, blocked_feature: str, context: Dict[str, Any] = None) -> UpgradePrompt:
        """Show upgrade prompt and track analytics."""
        prompt = self.get_upgrade_prompt(blocked_feature, context)
        
        # Track that prompt was shown
        prompt_key = f"{blocked_feature}_{prompt.recommended_tier.value}"
        if prompt_key not in self._upgrade_prompts_shown:
            self._upgrade_prompts_shown.add(prompt_key)
            track_upgrade_prompt(
                prompt_type=blocked_feature,
                feature_attempted=blocked_feature,
                user_action='shown'
            )
        
        return prompt
    
    def get_tier_info(self) -> Dict[str, Any]:
        """Get current tier information."""
        return {
            'current_tier': self.current_tier.value,
            'limits': {
                'max_file_lines': self.tier_limits.max_file_lines,
                'max_files_per_session': self.tier_limits.max_files_per_session,
                'export_formats': list(self.tier_limits.export_formats),
                'advanced_features': list(self.tier_limits.advanced_features),
                'performance_profiling': self.tier_limits.performance_profiling,
                'collaboration_features': self.tier_limits.collaboration_features,
                'priority_support': self.tier_limits.priority_support,
                'custom_themes': self.tier_limits.custom_themes
            },
            'session_usage': {
                'files_analyzed': self.session_usage['files_analyzed'],
                'exports_attempted': self.session_usage['exports_attempted'],
                'features_used': list(self.session_usage['features_used']),
                'session_duration_minutes': (time.time() - self.session_usage['session_start']) / 60
            }
        }
    
    def get_pricing_info(self) -> Dict[str, Any]:
        """Get pricing information for all tiers."""
        return {
            'tiers': {
                'free': {
                    'name': 'FlowLens Community',
                    'price': '$0/month',
                    'features': [
                        'Basic code analysis',
                        'Up to 100 lines per file',
                        'PNG export only',
                        'Standard themes',
                        'Community support'
                    ]
                },
                'pro': {
                    'name': 'FlowLens Professional',
                    'price': '$19/month',
                    'features': [
                        'Unlimited code analysis',
                        'All export formats',
                        'Advanced visualizations',
                        'Performance profiling',
                        'Custom themes',
                        'Priority support'
                    ]
                },
                'team': {
                    'name': 'FlowLens Team',
                    'price': '$49/user/month',
                    'features': [
                        'Everything in Pro',
                        'Team collaboration',
                        'Shared libraries',
                        'Git integration',
                        'Team analytics',
                        'SSO support'
                    ]
                },
                'enterprise': {
                    'name': 'FlowLens Enterprise',
                    'price': '$199/user/month',
                    'features': [
                        'Everything in Team',
                        'On-premise deployment',
                        'Custom integrations',
                        'Advanced security',
                        'Compliance reporting',
                        'Dedicated support'
                    ]
                }
            }
        }


# Global license manager instance
_license_manager: Optional[LicenseManager] = None


def get_license_manager() -> LicenseManager:
    """Get the global license manager instance."""
    global _license_manager
    if _license_manager is None:
        _license_manager = LicenseManager()
    return _license_manager


def check_feature_access(feature_name: str, context: Dict[str, Any] = None) -> tuple[bool, Optional[UpgradePrompt]]:
    """
    Check if user can access a feature.
    
    Returns:
        Tuple of (can_access, upgrade_prompt_if_blocked)
    """
    manager = get_license_manager()
    
    if feature_name == 'file_analysis':
        file_lines = context.get('file_lines', 0) if context else 0
        if not manager.can_analyze_file(file_lines):
            prompt = manager.show_upgrade_prompt('file_size_limit', context)
            return False, prompt
    
    elif feature_name == 'export':
        format = context.get('format', 'png') if context else 'png'
        if not manager.can_export_format(format):
            prompt = manager.show_upgrade_prompt('export_format', context)
            return False, prompt
    
    elif feature_name == 'advanced_feature':
        feature = context.get('feature', '') if context else ''
        if not manager.can_use_feature(feature):
            prompt = manager.show_upgrade_prompt('advanced_feature', context)
            return False, prompt
    
    elif feature_name == 'session_limit':
        if not manager.check_session_limits():
            prompt = manager.show_upgrade_prompt('session_limit', context)
            return False, prompt
    
    return True, None
