#!/usr/bin/env python3
"""
FlowLens Basic Usage Example

This example demonstrates the basic usage of FlowLens for analyzing
Python code, generating visualizations, and tracking execution.
"""

from flowlens import FlowAnalyzer, Visualizer
from pathlib import Path


def example_function(n):
    """Example function for analysis."""
    result = 0
    
    if n > 0:
        for i in range(n):
            if i % 2 == 0:
                result += i
            else:
                result -= i
    else:
        result = -1
    
    return result


def main():
    """Demonstrate FlowLens basic usage."""
    print("FlowLens Basic Usage Example")
    print("=" * 40)
    
    # Example 1: Analyze code string
    print("\n1. Analyzing code string...")
    
    code = '''
def factorial(n):
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)

result = factorial(5)
print(f"Factorial of 5 is {result}")
    '''
    
    # Create analyzer
    analyzer = FlowAnalyzer()
    
    # Analyze the code
    result = analyzer.analyze_code(code)
    print(f"Analysis completed in {result.parse_time:.3f}s")
    print(f"Found {len(result.functions)} functions")
    print(f"Found {len(result.decision_points)} decision points")
    print(f"Complexity score: {result.complexity_score}")
    
    # Example 2: Analyze this file
    print("\n2. Analyzing current file...")
    
    current_file = Path(__file__)
    file_result = analyzer.analyze_file(current_file)
    
    print(f"File: {file_result.filepath}")
    print(f"Total lines: {file_result.total_lines}")
    print(f"Functions found: {len(file_result.functions)}")
    
    for func in file_result.functions:
        print(f"  - {func['name']} (line {func['lineno']}, complexity: {func['complexity']})")
    
    # Example 3: Create visualizations
    print("\n3. Creating visualizations...")
    
    visualizer = Visualizer()
    
    # Note: In a real scenario, you would generate CFG and variable data
    # This is a simplified example showing the API
    print("Visualizer created successfully")
    print("Use visualizer.create_flow_diagram() and visualizer.create_heatmap() with actual data")
    
    print("\nExample completed successfully!")


if __name__ == "__main__":
    main()
