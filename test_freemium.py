#!/usr/bin/env python3
"""
Test script to verify FlowLens freemium functionality.

This script tests the licensing, analytics, and upgrade prompt features
to ensure the freemium business model is working correctly.
"""

import os
import tempfile
from pathlib import Path

# Set environment to free tier for testing
os.environ['FLOWLENS_TIER'] = 'free'
os.environ['FLOWLENS_ANALYTICS'] = 'true'

def test_free_tier_limits():
    """Test free tier limitations."""
    print("🧪 Testing Free Tier Limitations")
    print("=" * 40)
    
    from flowlens.core.licensing import get_license_manager
    from flowlens.core.analytics import get_analytics_collector
    
    # Test license manager
    license_manager = get_license_manager()
    tier_info = license_manager.get_tier_info()
    
    print(f"Current tier: {tier_info['current_tier']}")
    print(f"Max file lines: {tier_info['limits']['max_file_lines']}")
    print(f"Export formats: {tier_info['limits']['export_formats']}")
    
    # Test file size limit
    print("\n📏 Testing file size limits...")
    can_analyze_small = license_manager.can_analyze_file(50)
    can_analyze_large = license_manager.can_analyze_file(150)
    
    print(f"Can analyze 50 lines: {can_analyze_small}")
    print(f"Can analyze 150 lines: {can_analyze_large}")
    
    # Test export format restrictions
    print("\n📤 Testing export format restrictions...")
    can_export_png = license_manager.can_export_format('png')
    can_export_svg = license_manager.can_export_format('svg')
    
    print(f"Can export PNG: {can_export_png}")
    print(f"Can export SVG: {can_export_svg}")
    
    # Test upgrade prompt
    if not can_analyze_large:
        print("\n🚀 Testing upgrade prompt...")
        upgrade_prompt = license_manager.get_upgrade_prompt('file_size_limit', {'file_lines': 150})
        print(f"Upgrade prompt title: {upgrade_prompt.title}")
        print(f"Recommended tier: {upgrade_prompt.recommended_tier.value}")
        print(f"Benefits: {len(upgrade_prompt.benefits)} listed")


def test_analytics_tracking():
    """Test analytics tracking functionality."""
    print("\n📊 Testing Analytics Tracking")
    print("=" * 40)
    
    from flowlens.core.analytics import get_analytics_collector, track_feature_usage
    
    analytics = get_analytics_collector()
    
    # Track some test events
    track_feature_usage('test_feature_1', {'test_property': 'value1'})
    track_feature_usage('test_feature_2', {'test_property': 'value2'})
    
    # Get analytics summary
    summary = analytics.get_analytics_summary()
    
    print(f"Analytics enabled: {summary['analytics_enabled']}")
    print(f"Total events: {summary['total_events']}")
    print(f"Features used: {list(summary['feature_usage'].keys())}")
    print(f"Session duration: {summary['session_summary']['duration_seconds']:.2f}s")


def test_code_analysis_with_limits():
    """Test code analysis with tier limits."""
    print("\n🔍 Testing Code Analysis with Limits")
    print("=" * 40)
    
    from flowlens.core.ast_parser import ASTParser
    
    # Test small file (should work)
    small_code = '''
def hello():
    print("Hello, World!")
    return True

result = hello()
    '''.strip()
    
    print(f"Small code lines: {len(small_code.splitlines())}")
    
    try:
        parser = ASTParser()
        result = parser.parse_code(small_code)
        print("✅ Small file analysis: SUCCESS")
        print(f"   Functions found: {len(result.functions)}")
        print(f"   Parse time: {result.parse_time*1000:.2f}ms")
    except Exception as e:
        print(f"❌ Small file analysis: FAILED - {e}")
    
    # Test large file (should fail in free tier)
    large_code = '\n'.join([f"variable_{i} = {i} * 2" for i in range(120)])
    print(f"\nLarge code lines: {len(large_code.splitlines())}")
    
    try:
        parser = ASTParser()
        result = parser.parse_code(large_code)
        print("⚠️  Large file analysis: UNEXPECTED SUCCESS (should be blocked)")
    except ValueError as e:
        if "File analysis blocked" in str(e):
            print("✅ Large file analysis: CORRECTLY BLOCKED")
            print(f"   Reason: {e}")
        else:
            print(f"❌ Large file analysis: FAILED - {e}")
    except Exception as e:
        print(f"❌ Large file analysis: ERROR - {e}")


def test_export_restrictions():
    """Test export format restrictions."""
    print("\n📁 Testing Export Restrictions")
    print("=" * 40)
    
    from flowlens.core.licensing import check_feature_access
    
    # Test PNG export (should work)
    can_png, prompt_png = check_feature_access('export', {'format': 'png'})
    print(f"PNG export allowed: {can_png}")
    
    # Test SVG export (should be blocked)
    can_svg, prompt_svg = check_feature_access('export', {'format': 'svg'})
    print(f"SVG export allowed: {can_svg}")
    
    if prompt_svg:
        print(f"SVG blocked - upgrade prompt: {prompt_svg.title}")


def test_cli_integration():
    """Test CLI integration with freemium features."""
    print("\n💻 Testing CLI Integration")
    print("=" * 40)
    
    # Create a test file
    test_code = '''
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

# Test with small numbers
for i in range(5):
    print(f"fib({i}) = {fibonacci(i)}")
    '''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_code)
        test_file = Path(f.name)
    
    try:
        # Test analyze command
        print(f"Test file: {test_file}")
        print(f"Test file lines: {len(test_code.splitlines())}")
        
        # Import and test CLI command
        from flowlens.cli.commands import analyze_command
        
        # Mock context object
        class MockContext:
            def __init__(self):
                self.obj = {'verbose': False}
        
        ctx = MockContext()
        
        # Test analysis
        try:
            result = analyze_command(ctx, test_file, None, None, 'text', False)
            print(f"✅ CLI analyze command: SUCCESS (exit code: {result})")
        except Exception as e:
            print(f"❌ CLI analyze command: FAILED - {e}")
        
    finally:
        # Clean up
        test_file.unlink()


def main():
    """Run all freemium tests."""
    print("🎯 FlowLens Freemium Functionality Test")
    print("=" * 50)
    
    try:
        test_free_tier_limits()
        test_analytics_tracking()
        test_code_analysis_with_limits()
        test_export_restrictions()
        test_cli_integration()
        
        print("\n✅ All freemium tests completed!")
        print("\n📈 Analytics Summary:")
        
        from flowlens.core.analytics import get_analytics_collector
        analytics = get_analytics_collector()
        summary = analytics.get_analytics_summary()
        
        print(f"   Total events tracked: {summary['total_events']}")
        print(f"   Features used: {len(summary['feature_usage'])}")
        print(f"   Session duration: {summary['session_summary']['duration_seconds']:.2f}s")
        
        print("\n💰 Tier Information:")
        from flowlens.core.licensing import get_license_manager
        license_manager = get_license_manager()
        tier_info = license_manager.get_tier_info()
        
        print(f"   Current tier: {tier_info['current_tier']}")
        print(f"   Files analyzed: {tier_info['session_usage']['files_analyzed']}")
        print(f"   Export attempts: {tier_info['session_usage']['exports_attempted']}")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
