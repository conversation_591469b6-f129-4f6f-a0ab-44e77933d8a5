"""
FlowLens Performance Monitoring

This module provides performance monitoring and profiling capabilities
to ensure FlowLens meets its <200ms latency requirements.

Features:
- Operation timing and profiling
- Memory usage monitoring
- Performance bottleneck detection
- Latency tracking and reporting
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from contextlib import contextmanager
from functools import wraps


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    memory_before: float
    memory_after: float
    memory_delta: float
    cpu_percent: float
    error: Optional[str] = None
    
    @property
    def duration_ms(self) -> float:
        """Get duration in milliseconds."""
        return self.duration * 1000
    
    @property
    def memory_delta_mb(self) -> float:
        """Get memory delta in MB."""
        return self.memory_delta / (1024 * 1024)


class PerformanceMonitor:
    """
    Performance monitoring system for FlowLens operations.
    
    Tracks timing, memory usage, and system resources to ensure
    performance requirements are met.
    """
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self._metrics_history: deque = deque(maxlen=max_history)
        self._active_operations: Dict[str, Dict[str, Any]] = {}
        self._operation_stats: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.Lock()
        self._process = psutil.Process()
    
    def start_operation(self, operation_name: str) -> None:
        """Start monitoring an operation."""
        with self._lock:
            start_time = time.time()
            memory_info = self._process.memory_info()
            cpu_percent = self._process.cpu_percent()
            
            self._active_operations[operation_name] = {
                "start_time": start_time,
                "memory_before": memory_info.rss,
                "cpu_percent": cpu_percent,
            }
    
    def end_operation(self, operation_name: str, duration: Optional[float] = None, error: Optional[str] = None) -> PerformanceMetrics:
        """End monitoring an operation and record metrics."""
        with self._lock:
            if operation_name not in self._active_operations:
                raise ValueError(f"Operation '{operation_name}' was not started")
            
            op_data = self._active_operations.pop(operation_name)
            end_time = time.time()
            
            if duration is None:
                duration = end_time - op_data["start_time"]
            
            memory_info = self._process.memory_info()
            memory_after = memory_info.rss
            memory_delta = memory_after - op_data["memory_before"]
            
            metrics = PerformanceMetrics(
                operation_name=operation_name,
                start_time=op_data["start_time"],
                end_time=end_time,
                duration=duration,
                memory_before=op_data["memory_before"],
                memory_after=memory_after,
                memory_delta=memory_delta,
                cpu_percent=op_data["cpu_percent"],
                error=error,
            )
            
            # Store metrics
            self._metrics_history.append(metrics)
            self._operation_stats[operation_name].append(duration)
            
            return metrics
    
    def get_operation_stats(self, operation_name: str) -> Dict[str, float]:
        """Get statistics for a specific operation."""
        durations = self._operation_stats.get(operation_name, [])
        
        if not durations:
            return {}
        
        return {
            "count": len(durations),
            "avg_duration_ms": (sum(durations) / len(durations)) * 1000,
            "min_duration_ms": min(durations) * 1000,
            "max_duration_ms": max(durations) * 1000,
            "total_duration_ms": sum(durations) * 1000,
        }
    
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """Get statistics for all operations."""
        return {
            op_name: self.get_operation_stats(op_name)
            for op_name in self._operation_stats.keys()
        }
    
    def get_recent_metrics(self, count: int = 10) -> List[PerformanceMetrics]:
        """Get recent performance metrics."""
        with self._lock:
            return list(self._metrics_history)[-count:]
    
    def check_latency_requirement(self, operation_name: str, max_latency_ms: float = 200.0) -> bool:
        """Check if operation meets latency requirements."""
        stats = self.get_operation_stats(operation_name)
        if not stats:
            return True  # No data, assume OK
        
        return stats["avg_duration_ms"] <= max_latency_ms
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        report = {
            "timestamp": time.time(),
            "total_operations": len(self._metrics_history),
            "active_operations": list(self._active_operations.keys()),
            "operation_stats": self.get_all_stats(),
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": psutil.virtual_memory().total / (1024**3),
                "memory_available_gb": psutil.virtual_memory().available / (1024**3),
                "memory_percent": psutil.virtual_memory().percent,
            },
            "latency_compliance": {},
        }
        
        # Check latency compliance for each operation
        for op_name in self._operation_stats.keys():
            report["latency_compliance"][op_name] = self.check_latency_requirement(op_name)
        
        return report
    
    def clear_history(self) -> None:
        """Clear performance history."""
        with self._lock:
            self._metrics_history.clear()
            self._operation_stats.clear()


@contextmanager
def performance_timer(monitor: PerformanceMonitor, operation_name: str):
    """Context manager for timing operations."""
    monitor.start_operation(operation_name)
    try:
        yield
    except Exception as e:
        monitor.end_operation(operation_name, error=str(e))
        raise
    else:
        monitor.end_operation(operation_name)


def performance_profile(operation_name: str, monitor: Optional[PerformanceMonitor] = None):
    """Decorator for profiling function performance."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            if monitor is None:
                return func(*args, **kwargs)
            
            with performance_timer(monitor, operation_name):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


class LatencyTracker:
    """Specialized tracker for latency requirements."""
    
    def __init__(self, max_latency_ms: float = 200.0):
        self.max_latency_ms = max_latency_ms
        self._violations: List[Dict[str, Any]] = []
        self._lock = threading.Lock()
    
    def record_latency(self, operation: str, latency_ms: float, context: Optional[Dict[str, Any]] = None) -> bool:
        """Record latency and check for violations."""
        is_violation = latency_ms > self.max_latency_ms
        
        if is_violation:
            with self._lock:
                violation = {
                    "timestamp": time.time(),
                    "operation": operation,
                    "latency_ms": latency_ms,
                    "max_latency_ms": self.max_latency_ms,
                    "context": context or {},
                }
                self._violations.append(violation)
        
        return not is_violation
    
    def get_violations(self) -> List[Dict[str, Any]]:
        """Get all latency violations."""
        with self._lock:
            return self._violations.copy()
    
    def get_violation_rate(self) -> float:
        """Get the rate of latency violations."""
        # This would need to track total operations to calculate rate
        # For now, return count of violations
        return len(self._violations)
    
    def clear_violations(self) -> None:
        """Clear violation history."""
        with self._lock:
            self._violations.clear()


# Global performance monitor instance
_global_monitor = PerformanceMonitor()

def get_global_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    return _global_monitor
