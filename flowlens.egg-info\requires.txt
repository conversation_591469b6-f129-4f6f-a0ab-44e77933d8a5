click>=8.1.0
rich>=13.0.0
colorama>=0.4.6
networkx>=3.0
matplotlib>=3.6.0
graphviz>=0.20.0
numpy>=1.21.0
pandas>=1.5.0
psutil>=5.9.0
memory-profiler>=0.60.0
pyyaml>=6.0
toml>=0.10.2
jsonschema>=4.0.0
prompt-toolkit>=3.0.0
pygments>=2.13.0

[dev]
pytest>=7.2.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-benchmark>=4.0.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0
pylint>=2.15.0
pre-commit>=3.0.0

[docs]
sphinx>=5.3.0
sphinx-rtd-theme>=1.2.0
myst-parser>=0.18.0
sphinx-click>=4.4.0

[test]
pytest>=7.2.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-benchmark>=4.0.0
