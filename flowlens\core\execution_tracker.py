"""
FlowLens Execution Tracker

This module provides real-time execution tracking capabilities for Python code.
It monitors execution flow, tracks variable states, and provides step-through
debugging functionality.

Key Features:
- Real-time execution path tracking
- Step-by-step code execution monitoring
- Variable state capture at each step
- Execution history and replay
- Performance-optimized for <200ms latency
"""

import sys
import ast
import time
import threading
from typing import Dict, List, Optional, Any, Callable, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum

from .cfg_generator import ControlFlowGraph, CFGNode
from ..utils.performance import PerformanceMonitor
from .analytics import track_feature_usage, track_performance
from .licensing import get_license_manager


class ExecutionState(Enum):
    """Execution states."""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class ExecutionStep:
    """Represents a single execution step."""

    step_id: int
    line_number: int
    node_id: Optional[str]
    statement: str
    timestamp: float
    local_vars: Dict[str, Any] = field(default_factory=dict)
    global_vars: Dict[str, Any] = field(default_factory=dict)
    stack_trace: List[str] = field(default_factory=list)
    execution_time_ms: float = 0.0
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    
    @property
    def all_variables(self) -> Dict[str, Any]:
        """Get all variables (local and global)."""
        result = self.global_vars.copy()
        result.update(self.local_vars)
        return result


@dataclass
class ExecutionResult:
    """Complete execution result."""
    
    execution_id: str
    start_time: float
    end_time: float
    total_steps: int
    final_state: ExecutionState
    steps: List[ExecutionStep] = field(default_factory=list)
    error_info: Optional[Dict[str, Any]] = None
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_ms(self) -> float:
        """Get execution duration in milliseconds."""
        return (self.end_time - self.start_time) * 1000
    
    @property
    def execution_path(self) -> List[int]:
        """Get the execution path as line numbers."""
        return [step.line_number for step in self.steps]


class ExecutionTracker:
    """
    Real-time execution tracker for Python code.
    
    Provides comprehensive execution monitoring with variable tracking,
    step-through capabilities, and performance analysis.
    """
    
    def __init__(self, performance_monitor: Optional[PerformanceMonitor] = None):
        self.performance_monitor = performance_monitor
        self._execution_history: deque = deque(maxlen=1000)
        self._active_executions: Dict[str, 'ActiveExecution'] = {}
        self._step_callbacks: List[Callable[[ExecutionStep], None]] = []
        self._lock = threading.Lock()
        self._execution_counter = 0
        self._enable_profiling = self._check_profiling_availability()

    def _check_profiling_availability(self) -> bool:
        """Check if performance profiling is available for current tier."""
        license_manager = get_license_manager()
        return license_manager.tier_limits.performance_profiling

    def _get_performance_metrics(self) -> Dict[str, float]:
        """Get current performance metrics if profiling is enabled."""
        if not self._enable_profiling:
            return {}

        try:
            import psutil
            process = psutil.Process()

            return {
                'memory_usage_mb': process.memory_info().rss / 1024 / 1024,
                'cpu_usage_percent': process.cpu_percent()
            }
        except ImportError:
            return {}

    def start_tracking(self, code: str, cfg: Optional[ControlFlowGraph] = None,
                      globals_dict: Optional[Dict[str, Any]] = None) -> str:
        """
        Start tracking execution of Python code.
        
        Args:
            code: Python source code to execute
            cfg: Optional control flow graph for enhanced tracking
            globals_dict: Optional global variables dictionary
            
        Returns:
            Execution ID for tracking this execution
        """
        with self._lock:
            self._execution_counter += 1
            execution_id = f"exec_{self._execution_counter}_{int(time.time())}"

        # Track analytics
        track_feature_usage('execution_tracking_started', {
            'has_cfg': cfg is not None,
            'code_lines': len(code.splitlines()),
            'profiling_enabled': self._enable_profiling
        })

        # Create active execution
        active_exec = ActiveExecution(
            execution_id=execution_id,
            code=code,
            cfg=cfg,
            globals_dict=globals_dict or {},
            tracker=self
        )
        
        self._active_executions[execution_id] = active_exec
        
        if self.performance_monitor:
            self.performance_monitor.start_operation(f"execution_{execution_id}")
        
        return execution_id
    
    def step_execute(self, execution_id: str) -> Optional[ExecutionStep]:
        """
        Execute one step of the tracked code.
        
        Args:
            execution_id: ID of the execution to step
            
        Returns:
            ExecutionStep if successful, None if execution is complete
        """
        active_exec = self._active_executions.get(execution_id)
        if not active_exec:
            raise ValueError(f"Execution {execution_id} not found")
        
        return active_exec.step()
    
    def run_to_completion(self, execution_id: str, max_steps: int = 10000) -> ExecutionResult:
        """
        Run execution to completion.
        
        Args:
            execution_id: ID of the execution to run
            max_steps: Maximum number of steps to prevent infinite loops
            
        Returns:
            Complete execution result
        """
        active_exec = self._active_executions.get(execution_id)
        if not active_exec:
            raise ValueError(f"Execution {execution_id} not found")
        
        return active_exec.run_to_completion(max_steps)
    
    def pause_execution(self, execution_id: str) -> bool:
        """Pause an active execution."""
        active_exec = self._active_executions.get(execution_id)
        if active_exec:
            active_exec.pause()
            return True
        return False
    
    def resume_execution(self, execution_id: str) -> bool:
        """Resume a paused execution."""
        active_exec = self._active_executions.get(execution_id)
        if active_exec:
            active_exec.resume()
            return True
        return False
    
    def stop_execution(self, execution_id: str) -> Optional[ExecutionResult]:
        """Stop and finalize an execution."""
        active_exec = self._active_executions.pop(execution_id, None)
        if active_exec:
            result = active_exec.finalize()
            self._execution_history.append(result)
            
            if self.performance_monitor:
                self.performance_monitor.end_operation(f"execution_{execution_id}")
            
            return result
        return None
    
    def get_execution_state(self, execution_id: str) -> Optional[ExecutionState]:
        """Get current state of an execution."""
        active_exec = self._active_executions.get(execution_id)
        return active_exec.state if active_exec else None
    
    def get_current_step(self, execution_id: str) -> Optional[ExecutionStep]:
        """Get current execution step."""
        active_exec = self._active_executions.get(execution_id)
        return active_exec.current_step if active_exec else None
    
    def get_execution_history(self, limit: int = 10) -> List[ExecutionResult]:
        """Get recent execution history."""
        return list(self._execution_history)[-limit:]
    
    def add_step_callback(self, callback: Callable[[ExecutionStep], None]) -> None:
        """Add a callback to be called on each execution step."""
        self._step_callbacks.append(callback)
    
    def remove_step_callback(self, callback: Callable[[ExecutionStep], None]) -> None:
        """Remove a step callback."""
        if callback in self._step_callbacks:
            self._step_callbacks.remove(callback)
    
    def _notify_step_callbacks(self, step: ExecutionStep) -> None:
        """Notify all step callbacks."""
        for callback in self._step_callbacks:
            try:
                callback(step)
            except Exception:
                pass  # Don't let callback errors break execution


class ActiveExecution:
    """
    Represents an active execution being tracked.
    
    Handles the actual execution monitoring and step-by-step processing.
    """
    
    def __init__(self, execution_id: str, code: str, cfg: Optional[ControlFlowGraph],
                 globals_dict: Dict[str, Any], tracker: ExecutionTracker):
        self.execution_id = execution_id
        self.code = code
        self.cfg = cfg
        self.globals_dict = globals_dict.copy()
        self.tracker = tracker
        
        # Execution state
        self.state = ExecutionState.NOT_STARTED
        self.start_time = time.time()
        self.steps: List[ExecutionStep] = []
        self.current_step: Optional[ExecutionStep] = None
        self.step_counter = 0
        
        # Compile code for execution
        try:
            self.compiled_code = compile(code, '<flowlens>', 'exec')
            self.ast_tree = ast.parse(code)
        except SyntaxError as e:
            self.state = ExecutionState.ERROR
            self.error_info = {"type": "SyntaxError", "message": str(e)}
            raise
        
        # Set up tracing
        self._original_trace = sys.gettrace()
        self._locals_dict = {}
        self._current_frame = None
    
    def step(self) -> Optional[ExecutionStep]:
        """Execute one step."""
        if self.state == ExecutionState.COMPLETED:
            return None
        
        if self.state == ExecutionState.NOT_STARTED:
            self.state = ExecutionState.RUNNING
            sys.settrace(self._trace_function)
        
        if self.state == ExecutionState.PAUSED:
            self.state = ExecutionState.RUNNING
        
        # Execute until next step or completion
        try:
            if self.step_counter == 0:
                # First step - start execution
                exec(self.compiled_code, self.globals_dict, self._locals_dict)
            else:
                # Continue from where we left off
                # This is simplified - real implementation would need more sophisticated control
                pass
        except Exception as e:
            self.state = ExecutionState.ERROR
            self.error_info = {"type": type(e).__name__, "message": str(e)}
        finally:
            sys.settrace(self._original_trace)
        
        return self.current_step
    
    def run_to_completion(self, max_steps: int = 10000) -> ExecutionResult:
        """Run execution to completion."""
        self.state = ExecutionState.RUNNING
        sys.settrace(self._trace_function)
        
        try:
            exec(self.compiled_code, self.globals_dict, self._locals_dict)
            self.state = ExecutionState.COMPLETED
        except Exception as e:
            self.state = ExecutionState.ERROR
            self.error_info = {"type": type(e).__name__, "message": str(e)}
        finally:
            sys.settrace(self._original_trace)
        
        return self.finalize()
    
    def pause(self) -> None:
        """Pause execution."""
        self.state = ExecutionState.PAUSED
    
    def resume(self) -> None:
        """Resume execution."""
        if self.state == ExecutionState.PAUSED:
            self.state = ExecutionState.RUNNING
    
    def finalize(self) -> ExecutionResult:
        """Finalize execution and create result."""
        end_time = time.time()
        
        result = ExecutionResult(
            execution_id=self.execution_id,
            start_time=self.start_time,
            end_time=end_time,
            total_steps=len(self.steps),
            final_state=self.state,
            steps=self.steps.copy(),
            error_info=getattr(self, 'error_info', None),
            performance_metrics={
                "duration_ms": (end_time - self.start_time) * 1000,
                "steps_per_second": len(self.steps) / max(end_time - self.start_time, 0.001),
                "avg_step_time_ms": (end_time - self.start_time) * 1000 / max(len(self.steps), 1),
            }
        )
        
        return result
    
    def _trace_function(self, frame, event, arg):
        """Trace function for monitoring execution."""
        if event == 'line':
            step_start_time = time.time()
            
            # Create execution step
            self.step_counter += 1
            step = ExecutionStep(
                step_id=self.step_counter,
                line_number=frame.f_lineno,
                node_id=self._get_cfg_node_id(frame.f_lineno),
                statement=self._get_line_text(frame.f_lineno),
                timestamp=step_start_time,
                local_vars=self._safe_copy_vars(frame.f_locals),
                global_vars=self._safe_copy_vars(frame.f_globals),
                stack_trace=self._get_stack_trace(frame),
            )
            
            self.steps.append(step)
            self.current_step = step
            
            # Calculate step execution time
            step.execution_time_ms = (time.time() - step_start_time) * 1000
            
            # Notify callbacks
            self.tracker._notify_step_callbacks(step)
            
            # Check for pause
            if self.state == ExecutionState.PAUSED:
                return None
        
        return self._trace_function
    
    def _get_cfg_node_id(self, line_number: int) -> Optional[str]:
        """Get CFG node ID for a line number."""
        if not self.cfg:
            return None
        
        for node in self.cfg.nodes.values():
            if node.line_number == line_number:
                return node.id
        return None
    
    def _get_line_text(self, line_number: int) -> str:
        """Get text of a specific line."""
        lines = self.code.splitlines()
        if 1 <= line_number <= len(lines):
            return lines[line_number - 1].strip()
        return ""
    
    def _safe_copy_vars(self, vars_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Safely copy variables dictionary."""
        safe_vars = {}
        for key, value in vars_dict.items():
            if key.startswith('__') and key.endswith('__'):
                continue  # Skip dunder variables
            
            try:
                # Try to create a safe representation
                if isinstance(value, (int, float, str, bool, type(None))):
                    safe_vars[key] = value
                elif isinstance(value, (list, tuple, dict, set)):
                    # Limit size for performance
                    if len(str(value)) < 1000:
                        safe_vars[key] = value
                    else:
                        safe_vars[key] = f"<{type(value).__name__} of length {len(value)}>"
                else:
                    safe_vars[key] = f"<{type(value).__name__}>"
            except Exception:
                safe_vars[key] = "<unable to represent>"
        
        return safe_vars
    
    def _get_stack_trace(self, frame) -> List[str]:
        """Get current stack trace."""
        stack = []
        current_frame = frame
        
        while current_frame and len(stack) < 10:  # Limit stack depth
            filename = current_frame.f_code.co_filename
            line_number = current_frame.f_lineno
            function_name = current_frame.f_code.co_name
            
            stack.append(f"{filename}:{line_number} in {function_name}")
            current_frame = current_frame.f_back
        
        return stack
