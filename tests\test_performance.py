"""
FlowLens Performance Tests

This module contains performance tests to ensure FlowLens meets
its performance requirements:
- <200ms visualization latency for 100 lines of code
- 95% accuracy in execution path prediction
- Memory efficiency for large codebases
"""

import pytest
import time
import psutil
import os
from pathlib import Path

from flowlens.core import <PERSON><PERSON>arser, CFGGenerator, ExecutionTracker, VariableMonitor
from flowlens.visualization import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HeatmapRenderer
from flowlens.utils.performance import PerformanceMonitor


class TestLatencyRequirements:
    """Test latency requirements for FlowLens operations."""
    
    def setup_method(self):
        """Set up performance test fixtures."""
        self.performance_monitor = PerformanceMonitor()
        self.parser = ASTParser(enable_performance_monitoring=True)
        self.cfg_generator = CFGGenerator()
        
    def generate_code_lines(self, num_lines: int) -> str:
        """Generate Python code with specified number of lines."""
        lines = []
        
        # Add imports and setup
        lines.append("import math")
        lines.append("import random")
        lines.append("")
        
        # Add functions to reach target line count
        func_lines = 5  # Average lines per function
        num_functions = (num_lines - 3) // func_lines
        
        for i in range(num_functions):
            lines.append(f"def function_{i}(x):")
            lines.append(f"    if x > {i}:")
            lines.append(f"        return x * {i + 1}")
            lines.append(f"    else:")
            lines.append(f"        return x + {i}")
            
            if len(lines) >= num_lines:
                break
        
        # Pad with simple statements if needed
        while len(lines) < num_lines:
            lines.append(f"variable_{len(lines)} = {len(lines)}")
        
        return "\n".join(lines[:num_lines])
    
    @pytest.mark.benchmark
    def test_parsing_latency_100_lines(self):
        """Test that parsing 100 lines meets <200ms requirement."""
        code = self.generate_code_lines(100)
        
        start_time = time.time()
        result = self.parser.parse_code(code)
        parse_time = time.time() - start_time
        
        # Verify line count
        assert result.total_lines == 100
        
        # Check latency requirement
        latency_ms = parse_time * 1000
        assert latency_ms < 200, f"Parsing latency {latency_ms:.2f}ms exceeds 200ms requirement"
        
        # Verify parsing was successful
        assert result.ast_tree is not None
        assert len(result.functions) > 0
    
    @pytest.mark.benchmark
    def test_cfg_generation_latency(self):
        """Test CFG generation latency for 100-line code."""
        code = self.generate_code_lines(100)
        
        # Parse first
        parse_result = self.parser.parse_code(code)
        
        # Time CFG generation
        start_time = time.time()
        cfg = self.cfg_generator.generate_cfg(parse_result)
        cfg_time = time.time() - start_time
        
        # Check latency requirement
        latency_ms = cfg_time * 1000
        assert latency_ms < 200, f"CFG generation latency {latency_ms:.2f}ms exceeds 200ms requirement"
        
        # Verify CFG was generated
        assert cfg is not None
        assert len(cfg.nodes) > 0
        assert len(cfg.edges) > 0
    
    @pytest.mark.benchmark
    def test_end_to_end_latency(self):
        """Test end-to-end latency from parsing to visualization."""
        code = self.generate_code_lines(100)
        
        start_time = time.time()
        
        # Parse
        parse_result = self.parser.parse_code(code)
        
        # Generate CFG
        cfg = self.cfg_generator.generate_cfg(parse_result)
        
        # Track execution (simplified)
        tracker = ExecutionTracker()
        execution_id = tracker.start_tracking(code, cfg)
        
        end_time = time.time()
        total_latency = (end_time - start_time) * 1000
        
        # End-to-end should be under 500ms for 100 lines
        assert total_latency < 500, f"End-to-end latency {total_latency:.2f}ms exceeds 500ms"
        
        # Clean up
        tracker.stop_execution(execution_id)
    
    @pytest.mark.benchmark
    def test_visualization_latency(self):
        """Test visualization generation latency."""
        code = self.generate_code_lines(50)  # Smaller for visualization
        
        # Prepare data
        parse_result = self.parser.parse_code(code)
        cfg = self.cfg_generator.generate_cfg(parse_result)
        
        # Test flow diagram rendering
        renderer = FlowDiagramRenderer()
        
        start_time = time.time()
        # Note: In real test, you'd render to file or memory
        # Here we just test the setup time
        renderer.set_theme("default")
        renderer.set_layout("hierarchical")
        setup_time = time.time() - start_time
        
        setup_latency_ms = setup_time * 1000
        assert setup_latency_ms < 100, f"Visualization setup latency {setup_latency_ms:.2f}ms too high"


class TestAccuracyRequirements:
    """Test execution path prediction accuracy requirements."""
    
    def setup_method(self):
        """Set up accuracy test fixtures."""
        self.parser = ASTParser()
        self.cfg_generator = CFGGenerator()
        self.tracker = ExecutionTracker()
    
    def test_simple_linear_code_accuracy(self):
        """Test accuracy for simple linear code."""
        code = '''
x = 1
y = 2
z = x + y
result = z * 2
print(result)
        '''
        
        # Parse and generate CFG
        parse_result = self.parser.parse_code(code)
        cfg = self.cfg_generator.generate_cfg(parse_result)
        
        # Execute and track
        execution_id = self.tracker.start_tracking(code, cfg)
        execution_result = self.tracker.run_to_completion(execution_id)
        
        # Verify execution completed successfully
        assert execution_result.final_state.value in ['completed', 'error']
        assert execution_result.total_steps > 0
        
        # For linear code, we should have high accuracy
        # (In practice, you'd compare predicted vs actual execution paths)
        expected_min_steps = 4  # At least one step per statement
        assert execution_result.total_steps >= expected_min_steps
    
    def test_conditional_code_accuracy(self):
        """Test accuracy for code with conditionals."""
        code = '''
def test_conditionals(x):
    if x > 10:
        result = x * 2
    elif x > 5:
        result = x + 10
    else:
        result = x
    
    return result

# Test different paths
result1 = test_conditionals(15)  # First branch
result2 = test_conditionals(7)   # Second branch  
result3 = test_conditionals(3)   # Third branch
        '''
        
        # Parse and execute
        parse_result = self.parser.parse_code(code)
        cfg = self.cfg_generator.generate_cfg(parse_result)
        
        execution_id = self.tracker.start_tracking(code, cfg)
        execution_result = self.tracker.run_to_completion(execution_id)
        
        # Should execute all three function calls
        assert execution_result.total_steps > 10
        assert execution_result.final_state.value in ['completed', 'error']
    
    def test_loop_code_accuracy(self):
        """Test accuracy for code with loops."""
        code = '''
total = 0
for i in range(5):
    if i % 2 == 0:
        total += i
    else:
        total -= i

final_result = total
        '''
        
        # Parse and execute
        parse_result = self.parser.parse_code(code)
        cfg = self.cfg_generator.generate_cfg(parse_result)
        
        execution_id = self.tracker.start_tracking(code, cfg)
        execution_result = self.tracker.run_to_completion(execution_id)
        
        # Should have multiple steps for loop iterations
        assert execution_result.total_steps > 5
        assert execution_result.final_state.value in ['completed', 'error']


class TestMemoryEfficiency:
    """Test memory efficiency for large codebases."""
    
    def test_memory_usage_large_file(self):
        """Test memory usage for large Python files."""
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate large code (1000 lines)
        large_code = self._generate_large_code(1000)
        
        # Parse large code
        parser = ASTParser()
        result = parser.parse_code(large_code)
        
        # Check memory usage after parsing
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for 1000 lines)
        assert memory_increase < 100, f"Memory usage increased by {memory_increase:.1f}MB"
        
        # Verify parsing was successful
        assert result.total_lines == 1000
        assert len(result.functions) > 0
    
    def test_memory_cleanup(self):
        """Test that memory is properly cleaned up."""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create and destroy multiple parsers
        for i in range(10):
            parser = ASTParser()
            code = self._generate_large_code(100)
            result = parser.parse_code(code)
            
            # Force cleanup
            del parser
            del result
        
        # Check memory after cleanup
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory should not increase significantly
        assert memory_increase < 50, f"Memory leak detected: {memory_increase:.1f}MB increase"
    
    def _generate_large_code(self, num_lines: int) -> str:
        """Generate large Python code for memory testing."""
        lines = []
        
        # Add class definitions
        for class_num in range(num_lines // 20):
            lines.append(f"class Class{class_num}:")
            lines.append(f"    def __init__(self):")
            lines.append(f"        self.value = {class_num}")
            lines.append(f"")
            
            # Add methods
            for method_num in range(3):
                lines.append(f"    def method_{method_num}(self, x):")
                lines.append(f"        if x > {method_num}:")
                lines.append(f"            return x * {method_num + 1}")
                lines.append(f"        else:")
                lines.append(f"            return x + {method_num}")
                lines.append(f"")
        
        # Fill remaining lines with simple statements
        while len(lines) < num_lines:
            line_num = len(lines)
            lines.append(f"variable_{line_num} = {line_num} * 2")
        
        return "\n".join(lines[:num_lines])


class TestScalability:
    """Test scalability for different code sizes."""
    
    @pytest.mark.parametrize("num_lines", [10, 50, 100, 200, 500])
    def test_parsing_scalability(self, num_lines):
        """Test parsing performance scales linearly with code size."""
        # Generate code
        lines = [f"x_{i} = {i} * 2" for i in range(num_lines)]
        code = "\n".join(lines)
        
        # Parse and measure time
        parser = ASTParser()
        start_time = time.time()
        result = parser.parse_code(code)
        parse_time = time.time() - start_time
        
        # Calculate time per line
        time_per_line = parse_time / num_lines
        
        # Should be roughly linear (less than 2ms per line)
        assert time_per_line < 0.002, f"Parse time per line {time_per_line*1000:.2f}ms too high"
        
        # Verify parsing was successful
        assert result.total_lines == num_lines
        assert len(result.variables) == num_lines
    
    def test_cfg_scalability(self):
        """Test CFG generation scalability."""
        sizes = [10, 25, 50, 100]
        times = []
        
        for size in sizes:
            # Generate code with functions
            lines = []
            for i in range(size // 5):
                lines.extend([
                    f"def func_{i}(x):",
                    f"    if x > {i}:",
                    f"        return x * 2",
                    f"    else:",
                    f"        return x + 1"
                ])
            
            code = "\n".join(lines)
            
            # Parse and generate CFG
            parser = ASTParser()
            parse_result = parser.parse_code(code)
            
            cfg_generator = CFGGenerator()
            start_time = time.time()
            cfg = cfg_generator.generate_cfg(parse_result)
            cfg_time = time.time() - start_time
            
            times.append(cfg_time)
            
            # Verify CFG was generated
            assert cfg is not None
            assert len(cfg.nodes) > 0
        
        # Check that time doesn't grow exponentially
        # (This is a simple check - in practice you'd do more sophisticated analysis)
        for i in range(1, len(times)):
            growth_factor = times[i] / times[i-1]
            size_factor = sizes[i] / sizes[i-1]
            
            # Growth should be roughly linear (factor < 3x size factor)
            assert growth_factor < size_factor * 3, f"CFG generation time growing too fast"


@pytest.mark.benchmark
def test_overall_performance_benchmark():
    """Comprehensive performance benchmark test."""
    # Test data
    test_code = '''
class PerformanceTest:
    def __init__(self, data):
        self.data = data
        self.results = []
    
    def process_data(self):
        for item in self.data:
            try:
                if item > 10:
                    result = self.complex_calculation(item)
                else:
                    result = self.simple_calculation(item)
                
                self.results.append(result)
                
            except Exception as e:
                self.results.append(None)
    
    def complex_calculation(self, x):
        total = 0
        for i in range(x):
            if i % 2 == 0:
                total += i ** 2
            else:
                total -= i
        return total
    
    def simple_calculation(self, x):
        return x * 2 + 1

# Usage
test = PerformanceTest([1, 5, 15, 25, 3, 12, 8, 20])
test.process_data()
print(f"Results: {test.results}")
    '''
    
    # Performance monitoring
    monitor = PerformanceMonitor()
    
    # Full workflow benchmark
    start_time = time.time()
    
    # Parse
    parser = ASTParser()
    parse_result = parser.parse_code(test_code)
    
    # Generate CFG
    cfg_generator = CFGGenerator()
    cfg = cfg_generator.generate_cfg(parse_result)
    
    # Execute and track
    tracker = ExecutionTracker()
    execution_id = tracker.start_tracking(test_code, cfg)
    execution_result = tracker.run_to_completion(execution_id, max_steps=1000)
    
    # Monitor variables
    variable_monitor = VariableMonitor()
    variable_monitor.start_monitoring()
    variable_analysis = variable_monitor.process_execution_result(execution_result)
    
    total_time = time.time() - start_time
    
    # Performance assertions
    assert total_time < 2.0, f"Total workflow time {total_time:.2f}s too high"
    assert parse_result.parse_time < 0.1, f"Parse time {parse_result.parse_time:.3f}s too high"
    assert execution_result.duration_ms < 1000, f"Execution time {execution_result.duration_ms:.2f}ms too high"
    
    # Functionality assertions
    assert len(parse_result.classes) == 1
    assert len(parse_result.functions) >= 3
    assert execution_result.total_steps > 0
    assert variable_analysis['total_variables'] > 0
    
    print(f"Benchmark completed in {total_time:.3f}s")
    print(f"Parse: {parse_result.parse_time*1000:.1f}ms")
    print(f"Execution: {execution_result.duration_ms:.1f}ms")
    print(f"Variables: {variable_analysis['total_variables']}")
