#!/usr/bin/env python3
"""
FlowLens Web Application Test Script

This script tests the basic functionality of the FlowLens web application
to ensure all components are working correctly.
"""

import sys
import os
import time
import requests
import json
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_health_endpoint():
    """Test the health check endpoint."""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✓ Health check passed:", data)
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Health check failed: {e}")
        return False

def test_analyze_endpoint():
    """Test the code analysis endpoint."""
    test_code = """
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def main():
    for i in range(5):
        print(f"F({i}) = {fibonacci(i)}")

if __name__ == "__main__":
    main()
"""
    
    try:
        payload = {
            "code": test_code,
            "filename": "test.py"
        }
        
        response = requests.post(
            "http://localhost:8000/api/analyze",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✓ Code analysis passed")
                print(f"  - Functions found: {len(data['data']['functions'])}")
                print(f"  - Total lines: {data['data']['file_info']['total_lines']}")
                print(f"  - Complexity score: {data['data']['file_info']['complexity_score']}")
                return True
            else:
                print(f"✗ Code analysis failed: {data.get('error')}")
                return False
        else:
            print(f"✗ Code analysis failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Code analysis failed: {e}")
        return False

def test_mermaid_visualization():
    """Test Mermaid diagram generation."""
    test_code = """
def simple_function(x):
    if x > 0:
        return x * 2
    else:
        return 0
"""
    
    try:
        payload = {
            "code": test_code,
            "filename": "test.py",
            "visualization_type": "mermaid",
            "format": "mmd",
            "theme": "default",
            "direction": "TD"
        }
        
        response = requests.post(
            "http://localhost:8000/api/visualize",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✓ Mermaid visualization passed")
                print(f"  - Content type: {data['content_type']}")
                print(f"  - Filename: {data['filename']}")
                return True
            else:
                print(f"✗ Mermaid visualization failed: {data.get('error')}")
                return False
        else:
            print(f"✗ Mermaid visualization failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Mermaid visualization failed: {e}")
        return False

def test_tier_endpoint():
    """Test the tier information endpoint."""
    try:
        response = requests.get("http://localhost:8000/api/tier", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✓ Tier information passed")
                print(f"  - Tier: {data.get('tier', {}).get('name', 'Unknown')}")
                return True
            else:
                print(f"✗ Tier information failed: {data.get('error')}")
                return False
        else:
            print(f"✗ Tier information failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Tier information failed: {e}")
        return False

def test_main_page():
    """Test the main web page."""
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            content = response.text
            if "FlowLens" in content and "CodeMirror" in content:
                print("✓ Main page loaded successfully")
                return True
            else:
                print("✗ Main page content incomplete")
                return False
        else:
            print(f"✗ Main page failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Main page failed: {e}")
        return False

def wait_for_server(max_attempts=30):
    """Wait for the server to start."""
    print("⏳ Waiting for server to start...")
    for attempt in range(max_attempts):
        try:
            response = requests.get("http://localhost:8000/api/health", timeout=2)
            if response.status_code == 200:
                print("✓ Server is ready")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(1)
        print(f"   Attempt {attempt + 1}/{max_attempts}")
    
    print("✗ Server failed to start within timeout")
    return False

def main():
    """Run all tests."""
    print("🧪 FlowLens Web Application Test Suite")
    print("=" * 50)
    
    # Wait for server to be ready
    if not wait_for_server():
        print("\n❌ Server not available. Please start the server first:")
        print("   python flowlens_web/start_dev.py")
        return 1
    
    # Run tests
    tests = [
        ("Health Check", test_health_endpoint),
        ("Main Page", test_main_page),
        ("Code Analysis", test_analyze_endpoint),
        ("Mermaid Visualization", test_mermaid_visualization),
        ("Tier Information", test_tier_endpoint),
    ]
    
    passed = 0
    total = len(tests)
    
    print(f"\n🚀 Running {total} tests...\n")
    
    for test_name, test_func in tests:
        print(f"Testing {test_name}...")
        if test_func():
            passed += 1
        print()
    
    # Summary
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! FlowLens Web is working correctly.")
        return 0
    else:
        print(f"❌ {total - passed} tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
