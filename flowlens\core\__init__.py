"""
FlowLens Core Module

This module contains the core functionality for FlowLens including:
- AST parsing and analysis
- Control Flow Graph generation
- Execution tracking and monitoring
- Variable state management

All core algorithms are implemented from scratch without paid APIs.
"""

from .ast_parser import ASTParser, ParseResult
from .cfg_generator import CFGGenerator, ControlFlowGraph
from .execution_tracker import ExecutionTracker, ExecutionState
from .variable_monitor import VariableMonitor, VariableState

__all__ = [
    "ASTParser",
    "ParseResult", 
    "CFGGenerator",
    "ControlFlowGraph",
    "ExecutionTracker",
    "ExecutionState",
    "VariableMonitor",
    "VariableState",
]
