---
title: Control Flow: test_sample.py - _transform()
---
flowchart TD
    node_1(["START"])
    node_2{L71: if _is_array(value)}
    node_3["L72: output = []"]
    node_4{{L73: for val in value}}
    node_5["L73: for_exit"]
    node_6["L74: output.append(_relate(known..."]
    node_7["L75: return output"]
    node_8["L71: merge"]
    node_9{L77: if _is_object(value)}
    node_10["L78: obj = {}"]
    node_11{{L79: for key in value}}
    node_12["L79: for_exit"]
    node_13["L80: obj[key] = _relate(known, i..."]
    node_14["L81: return obj"]
    node_15["L77: merge"]
    node_16["L83: return value"]
    node_1 --> node_2
    node_2 --> node_3
    node_3 --> node_4
    node_4 --> node_6
    node_4 -->|iterate| node_4
    node_6 --> node_4
    node_4 -->|done| node_5
    node_5 --> node_7
    node_2 -->|True| node_2
    node_2 -->|False| node_8
    node_8 --> node_9
    node_9 --> node_10
    node_10 --> node_11
    node_11 --> node_13
    node_11 -->|iterate| node_11
    node_13 --> node_11
    node_11 -->|done| node_12
    node_12 --> node_14
    node_9 -->|True| node_9
    node_9 -->|False| node_15
    node_15 --> node_16
    classDef entry fill:#c8e6c9,stroke:#4caf50,stroke-width:2px
    classDef exit fill:#ffcdd2,stroke:#f44336,stroke-width:2px
    classDef condition fill:#ffe0b2,stroke:#ff9800,stroke-width:2px
    classDef statement fill:#bbdefb,stroke:#2196f3,stroke-width:2px
    classDef loop_header fill:#e1bee7,stroke:#9c27b0,stroke-width:2px