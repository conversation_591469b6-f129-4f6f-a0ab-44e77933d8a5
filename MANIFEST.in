# FlowLens Package Manifest
# This file specifies which files to include in the distribution package

# Include documentation files
include README.md
include LICENSE
include CHANGELOG.md
include CONTRIBUTING.md

# Include configuration files
include pyproject.toml
include setup.cfg
include requirements*.txt

# Include package data
recursive-include flowlens/config *.json *.yaml *.yml
recursive-include flowlens/templates *.html *.jinja2
recursive-include flowlens/static *.css *.js *.png *.svg

# Include test fixtures (but not test code itself)
recursive-include tests/fixtures *.py *.json *.yaml

# Include examples
recursive-include examples *.py *.md *.json

# Include documentation source
recursive-include docs *.md *.rst *.py
recursive-include docs/_static *
recursive-include docs/_templates *

# Exclude development and build files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude tox.ini
exclude Makefile

# Exclude CI/CD files
recursive-exclude .github *

# Exclude cache and build directories
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * .pytest_cache
recursive-exclude * .mypy_cache
recursive-exclude * .coverage
recursive-exclude * htmlcov
recursive-exclude * .tox
recursive-exclude * build
recursive-exclude * dist
recursive-exclude * *.egg-info

# Exclude IDE files
recursive-exclude * .vscode
recursive-exclude * .idea
recursive-exclude * *.swp
recursive-exclude * *.swo
recursive-exclude * *~

# Exclude OS files
recursive-exclude * .DS_Store
recursive-exclude * Thumbs.db
