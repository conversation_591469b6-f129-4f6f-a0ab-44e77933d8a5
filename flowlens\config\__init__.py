"""
FlowLens Configuration Module

This module provides configuration management for FlowLens,
including settings, themes, and user preferences.

Features:
- Application settings management
- Visualization themes and styling
- User configuration persistence
- Environment-specific configurations
"""

from .settings import Settings, get_settings, update_settings
from .themes import Theme, get_theme, list_themes

__all__ = [
    "Settings",
    "get_settings", 
    "update_settings",
    "Theme",
    "get_theme",
    "list_themes",
]
