"""
FlowLens Mermaid Diagram Renderer

This module generates Mermaid diagram syntax for control flow graphs,
system architecture, and workflow visualizations.

Features:
- Flowchart diagrams for control flow
- Class diagrams for system architecture
- Sequence diagrams for execution flow
- Professional styling and theming
- Multiple output formats
"""

import re
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Union
from dataclasses import dataclass
from enum import Enum

from ..core.cfg_generator import ControlFlowGraph, NodeType
from ..core.ast_parser import ParseResult
from ..core.analytics import track_feature_usage
from ..core.licensing import get_license_manager, check_feature_access
from .renderers import BaseRenderer


class MermaidDiagramType(Enum):
    """Types of Mermaid diagrams."""
    FLOWCHART = "flowchart"
    CLASS = "classDiagram"
    SEQUENCE = "sequenceDiagram"
    GITGRAPH = "gitgraph"
    JOURNEY = "journey"


@dataclass
class MermaidNode:
    """Represents a node in a Mermaid diagram."""
    id: str
    label: str
    shape: str = "rect"  # rect, round, circle, rhombus, hexagon, etc.
    style_class: Optional[str] = None
    
    def to_mermaid(self) -> str:
        """Convert to Mermaid syntax."""
        if self.shape == "rect":
            return f'{self.id}["{self.label}"]'
        elif self.shape == "round":
            return f'{self.id}("{self.label}")'
        elif self.shape == "circle":
            return f'{self.id}(("{self.label}"))'
        elif self.shape == "rhombus":
            return f'{self.id}{{{self.label}}}'
        elif self.shape == "hexagon":
            return f'{self.id}{{{{{self.label}}}}}'
        elif self.shape == "stadium":
            return f'{self.id}(["{self.label}"])'
        else:
            return f'{self.id}["{self.label}"]'


@dataclass
class MermaidEdge:
    """Represents an edge in a Mermaid diagram."""
    source: str
    target: str
    label: Optional[str] = None
    style: str = "solid"  # solid, dotted, thick
    
    def to_mermaid(self) -> str:
        """Convert to Mermaid syntax."""
        if self.style == "dotted":
            arrow = "-..->"
        elif self.style == "thick":
            arrow = "==>"
        else:
            arrow = "-->"
        
        if self.label:
            return f'{self.source} {arrow}|{self.label}| {self.target}'
        else:
            return f'{self.source} {arrow} {self.target}'


class MermaidRenderer(BaseRenderer):
    """
    Renders control flow graphs and system architecture as Mermaid diagrams.

    Generates clean, professional Mermaid syntax that can be rendered
    by various tools and platforms including GitHub, GitLab, and documentation sites.
    """

    def __init__(self):
        super().__init__()
        self.output_formats.extend(['mmd', 'html'])
        self.theme = "default"
        self.direction = "TD"  # Top-Down, Left-Right, etc.

    def render(self, data: Any, output_path: Path) -> None:
        """
        Render data to file. This is the abstract method implementation.

        Args:
            data: Data to render (CFG, ParseResult, etc.)
            output_path: Output file path
        """
        # Determine what type of data we're rendering
        if hasattr(data, 'nodes') and hasattr(data, 'edges'):
            # It's a CFG
            content = self.render_flowchart(data)
        elif hasattr(data, 'classes') and hasattr(data, 'functions'):
            # It's a ParseResult
            content = self.render_class_diagram(data)
        else:
            raise ValueError(f"Unsupported data type for rendering: {type(data)}")

        # Save to file
        format = output_path.suffix[1:].lower() if output_path.suffix else 'mmd'
        self.render_to_file(content, output_path, format)
        
    def set_theme(self, theme: str) -> None:
        """Set Mermaid theme."""
        valid_themes = ["default", "dark", "forest", "base", "neutral"]
        if theme in valid_themes:
            self.theme = theme
    
    def set_direction(self, direction: str) -> None:
        """Set diagram direction."""
        valid_directions = ["TD", "TB", "BT", "RL", "LR"]
        if direction in valid_directions:
            self.direction = direction
    
    def render_flowchart(self, cfg: ControlFlowGraph, title: Optional[str] = None) -> str:
        """
        Render control flow graph as Mermaid flowchart.
        
        Args:
            cfg: Control flow graph to render
            title: Optional diagram title
            
        Returns:
            Mermaid flowchart syntax
        """
        lines = []
        
        # Add title if provided
        if title:
            lines.append(f"---")
            lines.append(f"title: {title}")
            lines.append(f"---")
        
        # Start flowchart
        lines.append(f"flowchart {self.direction}")
        
        # Add nodes
        nodes = self._convert_cfg_to_mermaid_nodes(cfg)
        for node in nodes:
            lines.append(f"    {node.to_mermaid()}")
        
        # Add edges
        edges = self._convert_cfg_to_mermaid_edges(cfg)
        for edge in edges:
            lines.append(f"    {edge.to_mermaid()}")
        
        # Add styling
        lines.extend(self._get_flowchart_styling())
        
        return "\n".join(lines)
    
    def render_class_diagram(self, parse_result: ParseResult, title: Optional[str] = None) -> str:
        """
        Render system architecture as Mermaid class diagram.
        
        Args:
            parse_result: Parsed code structure
            title: Optional diagram title
            
        Returns:
            Mermaid class diagram syntax
        """
        lines = []
        
        # Add title if provided
        if title:
            lines.append(f"---")
            lines.append(f"title: {title}")
            lines.append(f"---")
        
        # Start class diagram
        lines.append("classDiagram")
        
        # Add classes
        for class_info in parse_result.classes:
            class_name = self._sanitize_name(class_info['name'])
            lines.append(f"    class {class_name} {{")
            
            # Add methods
            for method in class_info.get('methods', []):
                method_name = self._sanitize_name(method)
                lines.append(f"        +{method_name}()")
            
            lines.append("    }")
        
        # Add relationships (inheritance, composition, etc.)
        lines.extend(self._get_class_relationships(parse_result))
        
        # Add styling
        lines.extend(self._get_class_diagram_styling())
        
        return "\n".join(lines)
    
    def render_sequence_diagram(self, execution_steps: List[Any], title: Optional[str] = None) -> str:
        """
        Render execution flow as Mermaid sequence diagram.
        
        Args:
            execution_steps: List of execution steps
            title: Optional diagram title
            
        Returns:
            Mermaid sequence diagram syntax
        """
        lines = []
        
        # Add title if provided
        if title:
            lines.append(f"---")
            lines.append(f"title: {title}")
            lines.append(f"---")
        
        # Start sequence diagram
        lines.append("sequenceDiagram")
        
        # Add participants
        participants = self._extract_participants(execution_steps)
        for participant in participants:
            lines.append(f"    participant {participant}")
        
        # Add sequence interactions
        for i, step in enumerate(execution_steps[:20]):  # Limit to first 20 steps
            if hasattr(step, 'statement') and hasattr(step, 'line_number'):
                lines.append(f"    Main->>+Execution: Line {step.line_number}")
                lines.append(f"    Note over Execution: {step.statement[:50]}")
                lines.append(f"    Execution-->>-Main: Complete")
        
        return "\n".join(lines)
    
    def render_architecture_overview(self, parse_results: List[ParseResult], 
                                   title: Optional[str] = None) -> str:
        """
        Render system architecture overview as Mermaid flowchart.
        
        Args:
            parse_results: List of parsed files
            title: Optional diagram title
            
        Returns:
            Mermaid architecture diagram syntax
        """
        lines = []
        
        # Add title if provided
        if title:
            lines.append(f"---")
            lines.append(f"title: {title}")
            lines.append(f"---")
        
        # Start flowchart
        lines.append(f"flowchart {self.direction}")
        
        # Add modules/files as nodes
        for result in parse_results:
            if result.filepath:
                file_name = Path(result.filepath).stem
                sanitized_name = self._sanitize_name(file_name)
                
                # Determine node shape based on content
                if result.classes:
                    shape = "hexagon"  # Classes
                elif result.functions:
                    shape = "round"    # Functions
                else:
                    shape = "rect"     # Scripts
                
                node = MermaidNode(
                    id=sanitized_name,
                    label=f"{file_name}\\n{len(result.functions)}f {len(result.classes)}c",
                    shape=shape
                )
                lines.append(f"    {node.to_mermaid()}")
        
        # Add import relationships
        lines.extend(self._get_import_relationships(parse_results))
        
        # Add styling
        lines.extend(self._get_architecture_styling())
        
        return "\n".join(lines)
    
    def render_to_file(self, content: str, output_path: Path, format: str = "mmd") -> None:
        """
        Render Mermaid diagram to file with licensing checks.
        
        Args:
            content: Mermaid diagram content
            output_path: Output file path
            format: Output format (mmd, html, png, svg, pdf)
        """
        # Check licensing for advanced formats
        if format in ['html', 'svg', 'pdf']:
            can_export, upgrade_prompt = check_feature_access('export', {'format': format})
            if not can_export:
                track_feature_usage('mermaid_export_blocked', {
                    'format': format,
                    'reason': 'tier_limit'
                })
                raise ValueError(f"Export format '{format}' not available in your tier. {upgrade_prompt.message}")
        
        # Track export attempt
        track_feature_usage('mermaid_export_started', {
            'format': format,
            'content_length': len(content)
        })
        
        # Ensure output path has correct extension
        if not output_path.suffix:
            output_path = output_path.with_suffix(f".{format}")
        
        try:
            if format == "mmd":
                # Save raw Mermaid syntax
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            elif format == "html":
                # Generate HTML with embedded Mermaid
                html_content = self._generate_html_wrapper(content)
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
            
            else:
                # For other formats, save as .mmd and provide instructions
                mmd_path = output_path.with_suffix('.mmd')
                with open(mmd_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"Mermaid diagram saved to: {mmd_path}")
                print(f"To render as {format.upper()}, use: mermaid -i {mmd_path} -o {output_path}")
            
            # Record successful export
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=True)
            
            track_feature_usage('mermaid_export_completed', {
                'format': format,
                'file_size_kb': output_path.stat().st_size / 1024 if output_path.exists() else 0
            })
            
        except Exception as e:
            # Record failed export
            license_manager = get_license_manager()
            license_manager.record_export_attempt(format, success=False)
            raise
    
    def _convert_cfg_to_mermaid_nodes(self, cfg: ControlFlowGraph) -> List[MermaidNode]:
        """Convert CFG nodes to Mermaid nodes."""
        nodes = []
        
        for node_id, node in cfg.nodes.items():
            # Determine shape based on node type
            if node.node_type == NodeType.ENTRY:
                shape = "stadium"
            elif node.node_type == NodeType.EXIT:
                shape = "stadium"
            elif node.node_type == NodeType.CONDITION:
                shape = "rhombus"
            elif node.node_type == NodeType.LOOP_HEADER:
                shape = "hexagon"
            else:
                shape = "rect"
            
            # Create clean label
            label = self._create_node_label(node)
            
            mermaid_node = MermaidNode(
                id=self._sanitize_name(node_id),
                label=label,
                shape=shape,
                style_class=node.node_type.value
            )
            nodes.append(mermaid_node)
        
        return nodes
    
    def _convert_cfg_to_mermaid_edges(self, cfg: ControlFlowGraph) -> List[MermaidEdge]:
        """Convert CFG edges to Mermaid edges."""
        edges = []
        
        for edge in cfg.edges:
            source_id = self._sanitize_name(edge.source)
            target_id = self._sanitize_name(edge.target)
            
            # Determine edge style
            if edge.edge_type == "exception":
                style = "dotted"
            elif edge.condition in ["True", "False"]:
                style = "solid"
            else:
                style = "solid"
            
            mermaid_edge = MermaidEdge(
                source=source_id,
                target=target_id,
                label=edge.condition if edge.condition not in [None, ""] else None,
                style=style
            )
            edges.append(mermaid_edge)
        
        return edges
    
    def _create_node_label(self, node) -> str:
        """Create a clean, readable label for a node."""
        if node.node_type == NodeType.ENTRY:
            return "START"
        elif node.node_type == NodeType.EXIT:
            return "END"
        else:
            # Clean up the statement
            statement = node.statement.strip()
            if len(statement) > 30:
                statement = statement[:27] + "..."
            
            # Escape special characters
            statement = statement.replace('"', '\\"')
            
            return f"L{node.line_number}: {statement}"
    
    def _sanitize_name(self, name: str) -> str:
        """Sanitize name for Mermaid compatibility."""
        # Replace invalid characters with underscores
        sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        
        # Ensure it starts with a letter or underscore
        if sanitized and sanitized[0].isdigit():
            sanitized = f"_{sanitized}"
        
        return sanitized or "node"
    
    def _get_flowchart_styling(self) -> List[str]:
        """Get flowchart styling definitions."""
        styles = []
        
        if self.theme == "dark":
            styles.extend([
                "    classDef entry fill:#2d5a27,stroke:#4caf50,stroke-width:2px,color:#fff",
                "    classDef exit fill:#5d1a1a,stroke:#f44336,stroke-width:2px,color:#fff",
                "    classDef condition fill:#3d2914,stroke:#ff9800,stroke-width:2px,color:#fff",
                "    classDef statement fill:#1a237e,stroke:#2196f3,stroke-width:2px,color:#fff",
                "    classDef loop_header fill:#4a148c,stroke:#9c27b0,stroke-width:2px,color:#fff"
            ])
        else:
            styles.extend([
                "    classDef entry fill:#c8e6c9,stroke:#4caf50,stroke-width:2px",
                "    classDef exit fill:#ffcdd2,stroke:#f44336,stroke-width:2px",
                "    classDef condition fill:#ffe0b2,stroke:#ff9800,stroke-width:2px",
                "    classDef statement fill:#bbdefb,stroke:#2196f3,stroke-width:2px",
                "    classDef loop_header fill:#e1bee7,stroke:#9c27b0,stroke-width:2px"
            ])
        
        return styles
    
    def _get_class_relationships(self, parse_result: ParseResult) -> List[str]:
        """Get class relationships for class diagram."""
        relationships = []
        
        # Add inheritance relationships (simplified)
        for class_info in parse_result.classes:
            if 'bases' in class_info and class_info['bases']:
                class_name = self._sanitize_name(class_info['name'])
                for base in class_info['bases']:
                    base_name = self._sanitize_name(base)
                    relationships.append(f"    {base_name} <|-- {class_name}")
        
        return relationships
    
    def _get_class_diagram_styling(self) -> List[str]:
        """Get class diagram styling."""
        return [
            "    classDef default fill:#f9f9f9,stroke:#333,stroke-width:2px"
        ]
    
    def _extract_participants(self, execution_steps: List[Any]) -> List[str]:
        """Extract participants for sequence diagram."""
        return ["Main", "Execution"]
    
    def _get_import_relationships(self, parse_results: List[ParseResult]) -> List[str]:
        """Get import relationships between modules."""
        relationships = []
        
        for result in parse_results:
            if result.filepath and result.imports:
                source_name = self._sanitize_name(Path(result.filepath).stem)
                
                for import_info in result.imports:
                    if import_info['type'] == 'import':
                        target_name = self._sanitize_name(import_info['module'])
                        relationships.append(f"    {source_name} --> {target_name}")
        
        return relationships
    
    def _get_architecture_styling(self) -> List[str]:
        """Get architecture diagram styling."""
        return [
            "    classDef module fill:#e3f2fd,stroke:#2196f3,stroke-width:2px",
            "    classDef class fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px",
            "    classDef function fill:#e8f5e8,stroke:#4caf50,stroke-width:2px"
        ]
    
    def _generate_html_wrapper(self, mermaid_content: str) -> str:
        """Generate HTML wrapper for Mermaid diagram."""
        return f"""<!DOCTYPE html>
<html>
<head>
    <title>FlowLens Mermaid Diagram</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .mermaid {{ text-align: center; }}
        .header {{ text-align: center; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>FlowLens Code Visualization</h1>
        <p>Generated by FlowLens - Interactive Python Code Analysis</p>
    </div>
    
    <div class="mermaid">
{mermaid_content}
    </div>
    
    <script>
        mermaid.initialize({{ 
            startOnLoad: true,
            theme: '{self.theme}',
            flowchart: {{ useMaxWidth: true }}
        }});
    </script>
</body>
</html>"""
