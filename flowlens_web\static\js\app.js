// FlowLens Web Application JavaScript

class FlowLensApp {
    constructor() {
        this.codeEditor = null;
        this.currentResult = null;
        this.currentFilename = 'code.py';
        
        this.initializeComponents();
        this.bindEvents();
        this.loadTierInfo();
    }

    initializeComponents() {
        // Initialize CodeMirror
        const textarea = document.getElementById('codeEditor');
        this.codeEditor = CodeMirror.fromTextArea(textarea, {
            mode: 'python',
            theme: 'monokai',
            lineNumbers: true,
            indentUnit: 4,
            lineWrapping: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            foldGutter: true,
            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"]
        });

        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        // Set default code example
        this.codeEditor.setValue(`# Example Python code
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def main():
    for i in range(10):
        print(f"F({i}) = {fibonacci(i)}")

if __name__ == "__main__":
    main()`);
    }

    bindEvents() {
        // File upload
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileUpload(e);
        });

        // Visualization type change
        document.getElementById('visualizationType').addEventListener('change', (e) => {
            this.handleVisualizationTypeChange(e);
        });

        // Analyze button
        document.getElementById('analyzeBtn').addEventListener('click', () => {
            this.analyzeCode();
        });

        // Clear button
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearCode();
        });

        // Download button
        document.getElementById('downloadBtn').addEventListener('click', () => {
            this.downloadResult();
        });

        // Tier info button
        document.getElementById('tierInfoBtn').addEventListener('click', () => {
            this.showTierModal();
        });

        // Code editor change
        this.codeEditor.on('change', () => {
            this.updateFunctionList();
        });
    }

    async handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        try {
            this.showLoading();
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.codeEditor.setValue(result.code);
                this.currentFilename = result.filename;
                this.showSuccess(`File uploaded: ${result.filename} (${result.lines} lines)`);
                this.updateFunctionList();
            } else {
                this.showError('Upload failed: ' + result.error);
            }
        } catch (error) {
            this.showError('Upload failed: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    handleVisualizationTypeChange(event) {
        const type = event.target.value;
        const optionsDiv = document.getElementById('visualizationOptions');
        const formatSelect = document.getElementById('formatSelect');
        
        if (type === 'analyze') {
            optionsDiv.style.display = 'none';
        } else {
            optionsDiv.style.display = 'block';
            
            // Update format options based on visualization type
            formatSelect.innerHTML = '';
            if (type === 'mermaid') {
                formatSelect.innerHTML = `
                    <option value="mmd">Mermaid Text</option>
                    <option value="html">Interactive HTML</option>
                    <option value="png">PNG Image (Pro)</option>
                    <option value="svg">SVG Vector (Pro)</option>
                `;
            } else {
                formatSelect.innerHTML = `
                    <option value="png">PNG Image</option>
                    <option value="svg">SVG Vector (Pro)</option>
                    <option value="pdf">PDF Document (Pro)</option>
                `;
            }
        }
    }

    async analyzeCode() {
        const code = this.codeEditor.getValue().trim();
        if (!code) {
            this.showError('Please enter some Python code to analyze.');
            return;
        }

        const visualizationType = document.getElementById('visualizationType').value;
        const functionName = document.getElementById('functionSelect').value || null;

        try {
            this.showLoading();
            
            if (visualizationType === 'analyze') {
                await this.performAnalysis(code, functionName);
            } else {
                await this.generateVisualization(code, functionName, visualizationType);
            }
        } catch (error) {
            this.showError('Analysis failed: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async performAnalysis(code, functionName) {
        const response = await fetch('/api/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                code: code,
                filename: this.currentFilename,
                function_name: functionName
            })
        });

        const result = await response.json();
        
        if (result.success) {
            this.displayAnalysisResults(result.data);
            this.currentResult = result.data;
        } else {
            if (result.upgrade_prompt) {
                this.showUpgradePrompt(result.error, result.upgrade_prompt);
            } else {
                this.showError(result.error);
            }
        }
    }

    async generateVisualization(code, functionName, visualizationType) {
        const format = document.getElementById('formatSelect').value;
        const theme = document.getElementById('themeSelect').value;
        const direction = document.getElementById('directionSelect').value;

        const response = await fetch('/api/visualize', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                code: code,
                filename: this.currentFilename,
                function_name: functionName,
                visualization_type: visualizationType,
                format: format,
                theme: theme,
                direction: direction
            })
        });

        const result = await response.json();
        
        if (result.success) {
            this.displayVisualizationResults(result);
            this.currentResult = result;
        } else {
            if (result.upgrade_prompt) {
                this.showUpgradePrompt(result.error, result.upgrade_prompt);
            } else {
                this.showError(result.error);
            }
        }
    }

    displayAnalysisResults(data) {
        // Hide other states
        this.hideAllStates();
        
        // Show results
        document.getElementById('resultsContent').style.display = 'block';
        document.getElementById('analysisResults').style.display = 'block';
        document.getElementById('resultActions').style.display = 'block';

        // Update summary stats
        document.getElementById('totalLines').textContent = data.file_info.total_lines;
        document.getElementById('complexityScore').textContent = data.file_info.complexity_score;

        // Update functions table
        const functionsTable = document.getElementById('functionsTable').getElementsByTagName('tbody')[0];
        functionsTable.innerHTML = '';
        data.functions.forEach(func => {
            const row = functionsTable.insertRow();
            row.innerHTML = `
                <td><code>${func.name}</code></td>
                <td>${func.line}</td>
                <td>${func.args}</td>
                <td><span class="badge bg-${this.getComplexityColor(func.complexity)}">${func.complexity}</span></td>
            `;
        });

        // Update classes table
        const classesTable = document.getElementById('classesTable').getElementsByTagName('tbody')[0];
        classesTable.innerHTML = '';
        data.classes.forEach(cls => {
            const row = classesTable.insertRow();
            row.innerHTML = `
                <td><code>${cls.name}</code></td>
                <td>${cls.line}</td>
                <td>${cls.methods}</td>
                <td>${cls.bases.join(', ') || 'None'}</td>
            `;
        });

        // Update function select dropdown
        this.updateFunctionSelect(data.functions);
    }

    displayVisualizationResults(result) {
        // Hide other states
        this.hideAllStates();
        
        // Show results
        document.getElementById('resultsContent').style.display = 'block';
        document.getElementById('visualizationResults').style.display = 'block';
        document.getElementById('resultActions').style.display = 'block';

        if (result.content_type.startsWith('image/')) {
            // Display image
            document.getElementById('imageDisplay').style.display = 'block';
            document.getElementById('resultImage').src = `data:${result.content_type};base64,${result.content}`;
        } else if (result.content_type === 'text/html') {
            // Display HTML in iframe or new window
            const htmlContent = result.content;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            window.open(url, '_blank');
        } else if (result.content_type === 'text/plain' && result.filename.endsWith('.mmd')) {
            // Display Mermaid diagram
            document.getElementById('mermaidDisplay').style.display = 'block';
            this.renderMermaidDiagram(result.content);
        } else {
            // Display as text
            document.getElementById('textDisplay').style.display = 'block';
            document.getElementById('textContent').textContent = result.content;
        }
    }

    async renderMermaidDiagram(mermaidCode) {
        const element = document.getElementById('mermaidDiagram');
        element.innerHTML = '';
        
        try {
            const { svg } = await mermaid.render('mermaid-diagram', mermaidCode);
            element.innerHTML = svg;
        } catch (error) {
            element.innerHTML = `<div class="alert alert-warning">
                <h6>Mermaid Rendering Error</h6>
                <p>Could not render the diagram. Raw Mermaid code:</p>
                <pre><code>${mermaidCode}</code></pre>
            </div>`;
        }
    }

    updateFunctionList() {
        // This would parse the code to extract function names
        // For now, we'll just clear the select
        const functionSelect = document.getElementById('functionSelect');
        functionSelect.innerHTML = '<option value="">Analyze entire file</option>';
    }

    updateFunctionSelect(functions) {
        const functionSelect = document.getElementById('functionSelect');
        functionSelect.innerHTML = '<option value="">Analyze entire file</option>';
        
        functions.forEach(func => {
            const option = document.createElement('option');
            option.value = func.name;
            option.textContent = `${func.name}() - Line ${func.line}`;
            functionSelect.appendChild(option);
        });
    }

    clearCode() {
        this.codeEditor.setValue('');
        this.currentFilename = 'code.py';
        document.getElementById('fileInput').value = '';
        this.hideAllStates();
        document.getElementById('emptyState').style.display = 'block';
    }

    downloadResult() {
        if (!this.currentResult) return;

        let content, filename, mimeType;

        if (this.currentResult.content_type) {
            // Visualization result
            if (this.currentResult.content_type.startsWith('image/')) {
                // Convert base64 to blob
                const byteCharacters = atob(this.currentResult.content);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                content = new Blob([byteArray], { type: this.currentResult.content_type });
            } else {
                content = new Blob([this.currentResult.content], { type: this.currentResult.content_type });
            }
            filename = this.currentResult.filename;
            mimeType = this.currentResult.content_type;
        } else {
            // Analysis result
            content = new Blob([JSON.stringify(this.currentResult, null, 2)], { type: 'application/json' });
            filename = `${this.currentFilename.replace('.py', '')}_analysis.json`;
            mimeType = 'application/json';
        }

        const url = URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    async loadTierInfo() {
        try {
            const response = await fetch('/api/tier');
            const result = await response.json();
            
            if (result.success) {
                this.updateTierDisplay(result.tier, result.usage);
            }
        } catch (error) {
            console.error('Failed to load tier info:', error);
        }
    }

    updateTierDisplay(tierInfo, usage) {
        const tierStatus = document.getElementById('tierStatus');
        tierStatus.textContent = `${tierInfo.name} Tier`;
        tierStatus.className = `tier-${tierInfo.name.toLowerCase()}`;
    }

    async showTierModal() {
        const modal = new bootstrap.Modal(document.getElementById('tierModal'));
        modal.show();
        
        try {
            const response = await fetch('/api/tier');
            const result = await response.json();
            
            const tierInfo = document.getElementById('tierInfo');
            if (result.success) {
                tierInfo.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Current Tier</h6>
                            <p class="h4 text-primary">${result.tier.name}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Usage This Month</h6>
                            <p>${result.usage.files_analyzed || 0} files analyzed</p>
                            <p>${result.usage.exports_created || 0} exports created</p>
                        </div>
                    </div>
                    <hr>
                    <h6>Tier Limits</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>File analysis up to ${result.tier.max_lines || 100} lines</li>
                        <li><i class="fas fa-${result.tier.png_export ? 'check text-success' : 'times text-danger'} me-2"></i>PNG exports</li>
                        <li><i class="fas fa-${result.tier.svg_export ? 'check text-success' : 'times text-danger'} me-2"></i>SVG exports</li>
                        <li><i class="fas fa-${result.tier.pdf_export ? 'check text-success' : 'times text-danger'} me-2"></i>PDF exports</li>
                    </ul>
                `;
            } else {
                tierInfo.innerHTML = '<div class="alert alert-danger">Failed to load tier information</div>';
            }
        } catch (error) {
            document.getElementById('tierInfo').innerHTML = '<div class="alert alert-danger">Failed to load tier information</div>';
        }
    }

    showLoading() {
        this.hideAllStates();
        document.getElementById('loadingState').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingState').style.display = 'none';
    }

    showError(message) {
        this.hideAllStates();
        document.getElementById('errorState').style.display = 'block';
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('upgradePrompt').style.display = 'none';
    }

    showUpgradePrompt(error, upgradeMessage) {
        this.hideAllStates();
        document.getElementById('errorState').style.display = 'block';
        document.getElementById('errorMessage').textContent = error;
        document.getElementById('upgradePrompt').style.display = 'block';
        document.getElementById('upgradeMessage').textContent = upgradeMessage;
    }

    showSuccess(message) {
        // You could implement a toast notification here
        console.log('Success:', message);
    }

    hideAllStates() {
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('errorState').style.display = 'none';
        document.getElementById('resultsContent').style.display = 'none';
        document.getElementById('analysisResults').style.display = 'none';
        document.getElementById('visualizationResults').style.display = 'none';
        document.getElementById('imageDisplay').style.display = 'none';
        document.getElementById('mermaidDisplay').style.display = 'none';
        document.getElementById('textDisplay').style.display = 'none';
        document.getElementById('resultActions').style.display = 'none';
    }

    getComplexityColor(complexity) {
        if (complexity <= 2) return 'success';
        if (complexity <= 5) return 'warning';
        return 'danger';
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FlowLensApp();
});
