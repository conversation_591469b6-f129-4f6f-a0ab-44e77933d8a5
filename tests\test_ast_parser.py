"""
Tests for FlowLens AST Parser

This module contains comprehensive tests for the AST parsing functionality,
ensuring accurate code analysis and structure extraction.
"""

import pytest
import ast
from pathlib import Path
from flowlens.core.ast_parser import ASTParser, ParseResult
from flowlens.utils.validators import CodeValidator


class TestASTParser:
    """Test cases for AST Parser functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = ASTParser()
        self.validator = CodeValidator()
    
    def test_parse_simple_code(self):
        """Test parsing simple Python code."""
        code = """
def hello_world():
    print("Hello, World!")
    return True

x = 5
y = x + 10
        """.strip()
        
        result = self.parser.parse_code(code)
        
        assert isinstance(result, ParseResult)
        assert result.source_code == code
        assert result.ast_tree is not None
        assert len(result.functions) == 1
        assert result.functions[0]['name'] == 'hello_world'
        assert 'x' in result.variables
        assert 'y' in result.variables
    
    def test_parse_complex_code(self):
        """Test parsing complex Python code with control structures."""
        code = """
class Calculator:
    def __init__(self):
        self.result = 0
    
    def add(self, x, y):
        if x > 0 and y > 0:
            self.result = x + y
        else:
            self.result = 0
        return self.result
    
    def process_list(self, numbers):
        total = 0
        for num in numbers:
            if num % 2 == 0:
                total += num
            else:
                continue
        return total

def main():
    calc = Calculator()
    try:
        result = calc.add(5, 3)
        print(f"Result: {result}")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        print("Done")

if __name__ == "__main__":
    main()
        """.strip()
        
        result = self.parser.parse_code(code)
        
        # Check basic structure
        assert len(result.classes) == 1
        assert result.classes[0]['name'] == 'Calculator'
        assert len(result.functions) >= 3  # __init__, add, process_list, main
        
        # Check control structures
        assert len(result.decision_points) >= 2  # if statements
        assert len(result.loops) >= 1  # for loop
        assert len(result.exception_handlers) >= 1  # try-except
        
        # Check complexity
        assert result.complexity_score > 0
    
    def test_parse_file(self, tmp_path):
        """Test parsing Python file."""
        # Create test file
        test_file = tmp_path / "test_code.py"
        code = """
def factorial(n):
    if n <= 1:
        return 1
    else:
        return n * factorial(n - 1)

result = factorial(5)
print(result)
        """.strip()
        
        test_file.write_text(code)
        
        # Parse file
        result = self.parser.parse_file(test_file)
        
        assert result.filepath == str(test_file)
        assert len(result.functions) == 1
        assert result.functions[0]['name'] == 'factorial'
        assert len(result.decision_points) >= 1
    
    def test_parse_invalid_syntax(self):
        """Test parsing code with syntax errors."""
        invalid_code = """
def broken_function(
    print("This is broken")
        """
        
        with pytest.raises(SyntaxError):
            self.parser.parse_code(invalid_code)
    
    def test_parse_empty_code(self):
        """Test parsing empty code."""
        result = self.parser.parse_code("")
        
        assert result.total_lines == 0
        assert len(result.functions) == 0
        assert len(result.classes) == 0
        assert len(result.variables) == 0
    
    def test_performance_requirement(self):
        """Test that parsing meets performance requirements."""
        # Generate code with 100 lines
        lines = []
        for i in range(100):
            if i % 10 == 0:
                lines.append(f"def function_{i}():")
                lines.append(f"    return {i}")
            else:
                lines.append(f"x_{i} = {i} * 2")
        
        code = "\n".join(lines)
        
        # Parse and check timing
        result = self.parser.parse_code(code)
        
        # Should be under 200ms for 100 lines
        assert result.parse_time < 0.2, f"Parse time {result.parse_time:.3f}s exceeds 200ms requirement"
        assert result.total_lines == 100
    
    def test_function_complexity_calculation(self):
        """Test function complexity calculation."""
        code = """
def complex_function(x, y, z):
    if x > 0:
        if y > 0:
            if z > 0:
                return x + y + z
            else:
                return x + y
        else:
            return x
    else:
        for i in range(10):
            if i % 2 == 0:
                x += i
        return x
        """.strip()
        
        result = self.parser.parse_code(code)
        
        assert len(result.functions) == 1
        func = result.functions[0]
        assert func['complexity'] > 1  # Should have complexity > 1 due to nested conditions
    
    def test_import_detection(self):
        """Test import statement detection."""
        code = """
import os
import sys
from pathlib import Path
from typing import Dict, List
import numpy as np
        """.strip()
        
        result = self.parser.parse_code(code)
        
        assert len(result.imports) == 5
        
        # Check specific imports
        import_modules = [imp['module'] for imp in result.imports if imp['type'] == 'import']
        assert 'os' in import_modules
        assert 'sys' in import_modules
        
        from_imports = [imp for imp in result.imports if imp['type'] == 'from_import']
        assert any(imp['module'] == 'pathlib' and imp['name'] == 'Path' for imp in from_imports)
    
    def test_variable_detection(self):
        """Test variable detection and tracking."""
        code = """
x = 5
y = x + 10
z: int = 20
w += 5

def func():
    local_var = "hello"
    return local_var

class MyClass:
    def __init__(self):
        self.instance_var = 42
        """.strip()
        
        result = self.parser.parse_code(code)
        
        # Should detect module-level variables
        assert 'x' in result.variables
        assert 'y' in result.variables
        assert 'z' in result.variables
        assert 'w' in result.variables
    
    def test_decorator_detection(self):
        """Test decorator detection."""
        code = """
@property
def getter(self):
    return self._value

@staticmethod
@cache
def static_method():
    return "cached"
        """.strip()
        
        result = self.parser.parse_code(code)
        
        assert len(result.functions) == 2
        
        # Check decorators
        getter_func = next(f for f in result.functions if f['name'] == 'getter')
        assert 'property' in getter_func['decorators']
        
        static_func = next(f for f in result.functions if f['name'] == 'static_method')
        assert 'staticmethod' in static_func['decorators']
        assert 'cache' in static_func['decorators']
    
    def test_async_function_detection(self):
        """Test async function detection."""
        code = """
async def async_function():
    await some_operation()
    return "done"

def regular_function():
    return "sync"
        """.strip()
        
        result = self.parser.parse_code(code)
        
        assert len(result.functions) == 2
        
        async_func = next(f for f in result.functions if f['name'] == 'async_function')
        assert async_func['is_async'] is True
        
        regular_func = next(f for f in result.functions if f['name'] == 'regular_function')
        assert regular_func['is_async'] is False


@pytest.fixture
def sample_code_file(tmp_path):
    """Create a sample Python file for testing."""
    content = """
#!/usr/bin/env python3
'''
Sample Python module for testing FlowLens AST parser.
'''

import math
from typing import List, Optional

class MathUtils:
    '''Utility class for mathematical operations.'''
    
    def __init__(self):
        self.pi = math.pi
    
    def circle_area(self, radius: float) -> float:
        '''Calculate circle area.'''
        if radius < 0:
            raise ValueError("Radius cannot be negative")
        return self.pi * radius ** 2
    
    def fibonacci(self, n: int) -> List[int]:
        '''Generate Fibonacci sequence.'''
        if n <= 0:
            return []
        elif n == 1:
            return [0]
        elif n == 2:
            return [0, 1]
        
        fib = [0, 1]
        for i in range(2, n):
            fib.append(fib[i-1] + fib[i-2])
        
        return fib

def main():
    '''Main function.'''
    utils = MathUtils()
    
    try:
        area = utils.circle_area(5.0)
        print(f"Circle area: {area}")
        
        fib_seq = utils.fibonacci(10)
        print(f"Fibonacci: {fib_seq}")
        
    except ValueError as e:
        print(f"Error: {e}")
    finally:
        print("Calculation complete")

if __name__ == "__main__":
    main()
    """
    
    file_path = tmp_path / "sample_math.py"
    file_path.write_text(content)
    return file_path


def test_comprehensive_file_parsing(sample_code_file):
    """Test comprehensive parsing of a realistic Python file."""
    parser = ASTParser()
    result = parser.parse_file(sample_code_file)
    
    # Basic structure checks
    assert result.filepath == str(sample_code_file)
    assert result.total_lines > 0
    assert len(result.classes) == 1
    assert len(result.functions) >= 3  # __init__, circle_area, fibonacci, main
    
    # Class checks
    math_utils_class = result.classes[0]
    assert math_utils_class['name'] == 'MathUtils'
    assert 'circle_area' in math_utils_class['methods']
    assert 'fibonacci' in math_utils_class['methods']
    
    # Function checks
    function_names = [f['name'] for f in result.functions]
    assert 'main' in function_names
    assert '__init__' in function_names
    
    # Control structure checks
    assert len(result.decision_points) >= 3  # Multiple if statements
    assert len(result.loops) >= 1  # for loop in fibonacci
    assert len(result.exception_handlers) >= 1  # try-except in main
    
    # Import checks
    assert len(result.imports) >= 2  # math and typing imports
    
    # Performance check
    assert result.parse_time < 0.2  # Should be under 200ms
