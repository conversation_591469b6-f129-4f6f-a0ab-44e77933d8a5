"""
FlowLens Input Validators

This module provides comprehensive input validation for FlowLens,
ensuring safe and reliable operation with user-provided data.

Features:
- Python code validation and sanitization
- File path validation and security checks
- Configuration parameter validation
- Input sanitization for CLI arguments
"""

import ast
import re
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """Result of a validation operation."""
    
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    sanitized_value: Optional[Any] = None
    
    @property
    def has_errors(self) -> bool:
        """Check if validation has errors."""
        return len(self.errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        """Check if validation has warnings."""
        return len(self.warnings) > 0


class InputValidator:
    """
    General input validator for FlowLens.
    
    Provides validation for various types of user input including
    file paths, configuration values, and CLI arguments.
    """
    
    def __init__(self):
        self.max_path_length = 260  # Windows MAX_PATH limit
        self.allowed_extensions = {'.py', '.pyw'}
        self.dangerous_patterns = [
            r'\.\./',  # Directory traversal
            r'~/',     # Home directory access
            r'/etc/',  # System directories
            r'/proc/', # Process information
            r'/sys/',  # System information
        ]
    
    def validate_file_path(self, path: Union[str, Path]) -> ValidationResult:
        """
        Validate a file path for security and accessibility.
        
        Args:
            path: File path to validate
            
        Returns:
            ValidationResult with validation status and sanitized path
        """
        errors = []
        warnings = []
        
        try:
            path_obj = Path(path)
            path_str = str(path)
            
            # Check path length
            if len(path_str) > self.max_path_length:
                errors.append(f"Path too long: {len(path_str)} > {self.max_path_length}")
            
            # Check for dangerous patterns
            for pattern in self.dangerous_patterns:
                if re.search(pattern, path_str):
                    errors.append(f"Potentially dangerous path pattern: {pattern}")
            
            # Check if path exists
            if not path_obj.exists():
                errors.append(f"Path does not exist: {path}")
            elif path_obj.is_file():
                # Check file extension
                if path_obj.suffix.lower() not in self.allowed_extensions:
                    warnings.append(f"Unusual file extension: {path_obj.suffix}")
                
                # Check file size
                try:
                    size_mb = path_obj.stat().st_size / (1024 * 1024)
                    if size_mb > 10:
                        warnings.append(f"Large file: {size_mb:.1f}MB")
                except OSError as e:
                    errors.append(f"Cannot access file: {e}")
            
            # Resolve and sanitize path
            try:
                sanitized_path = path_obj.resolve()
            except OSError as e:
                errors.append(f"Cannot resolve path: {e}")
                sanitized_path = path_obj
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                sanitized_value=sanitized_path
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Path validation error: {e}"],
                warnings=[],
                sanitized_value=None
            )
    
    def validate_directory_path(self, path: Union[str, Path]) -> ValidationResult:
        """
        Validate a directory path.
        
        Args:
            path: Directory path to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = self.validate_file_path(path)
        
        if result.is_valid and result.sanitized_value:
            if not result.sanitized_value.is_dir():
                result.errors.append(f"Path is not a directory: {path}")
                result.is_valid = False
        
        return result
    
    def validate_output_path(self, path: Union[str, Path]) -> ValidationResult:
        """
        Validate an output file path (may not exist yet).
        
        Args:
            path: Output file path to validate
            
        Returns:
            ValidationResult with validation status
        """
        errors = []
        warnings = []
        
        try:
            path_obj = Path(path)
            
            # Check parent directory exists and is writable
            parent_dir = path_obj.parent
            if not parent_dir.exists():
                errors.append(f"Parent directory does not exist: {parent_dir}")
            elif not parent_dir.is_dir():
                errors.append(f"Parent path is not a directory: {parent_dir}")
            else:
                # Check if directory is writable
                try:
                    test_file = parent_dir / ".flowlens_write_test"
                    test_file.touch()
                    test_file.unlink()
                except OSError:
                    errors.append(f"Directory is not writable: {parent_dir}")
            
            # Check if file already exists
            if path_obj.exists():
                warnings.append(f"Output file already exists: {path}")
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                sanitized_value=path_obj.resolve() if len(errors) == 0 else None
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Output path validation error: {e}"],
                warnings=[],
                sanitized_value=None
            )
    
    def validate_config_value(self, key: str, value: Any, expected_type: type) -> ValidationResult:
        """
        Validate a configuration value.
        
        Args:
            key: Configuration key name
            value: Value to validate
            expected_type: Expected type for the value
            
        Returns:
            ValidationResult with validation status
        """
        errors = []
        warnings = []
        sanitized_value = value
        
        # Type checking
        if not isinstance(value, expected_type):
            try:
                # Try to convert
                if expected_type == int:
                    sanitized_value = int(value)
                elif expected_type == float:
                    sanitized_value = float(value)
                elif expected_type == bool:
                    if isinstance(value, str):
                        sanitized_value = value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        sanitized_value = bool(value)
                elif expected_type == str:
                    sanitized_value = str(value)
                else:
                    errors.append(f"Cannot convert {key} to {expected_type.__name__}")
            except (ValueError, TypeError) as e:
                errors.append(f"Invalid type for {key}: {e}")
        
        # Value-specific validation
        if key == "max_latency_ms" and isinstance(sanitized_value, (int, float)):
            if sanitized_value <= 0:
                errors.append("max_latency_ms must be positive")
            elif sanitized_value > 10000:
                warnings.append("max_latency_ms is very high (>10s)")
        
        elif key == "max_file_size_mb" and isinstance(sanitized_value, (int, float)):
            if sanitized_value <= 0:
                errors.append("max_file_size_mb must be positive")
            elif sanitized_value > 100:
                warnings.append("max_file_size_mb is very high (>100MB)")
        
        elif key == "theme" and isinstance(sanitized_value, str):
            allowed_themes = {"default", "dark", "light", "colorful", "minimal"}
            if sanitized_value not in allowed_themes:
                warnings.append(f"Unknown theme: {sanitized_value}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            sanitized_value=sanitized_value
        )


class CodeValidator:
    """
    Specialized validator for Python code.
    
    Provides comprehensive validation and security checking for
    Python source code before analysis.
    """
    
    def __init__(self):
        self.max_code_length = 1_000_000  # 1MB of code
        self.max_ast_nodes = 10_000       # Prevent AST bombs
        self.dangerous_imports = {
            'os', 'subprocess', 'sys', 'eval', 'exec', 'compile',
            'importlib', '__import__', 'globals', 'locals', 'vars',
            'open', 'file', 'input', 'raw_input'
        }
        self.dangerous_functions = {
            'eval', 'exec', 'compile', '__import__', 'getattr',
            'setattr', 'delattr', 'hasattr', 'globals', 'locals'
        }
    
    def validate_code_string(self, code: str) -> ValidationResult:
        """
        Validate Python source code string.
        
        Args:
            code: Python source code to validate
            
        Returns:
            ValidationResult with validation status and sanitized code
        """
        errors = []
        warnings = []
        sanitized_code = code
        
        # Basic checks
        if not code or not code.strip():
            errors.append("Code is empty")
            return ValidationResult(False, errors, warnings, None)
        
        if len(code) > self.max_code_length:
            errors.append(f"Code too long: {len(code)} > {self.max_code_length}")
        
        # Syntax validation
        try:
            tree = ast.parse(code)
        except SyntaxError as e:
            errors.append(f"Syntax error at line {e.lineno}: {e.msg}")
            return ValidationResult(False, errors, warnings, None)
        except Exception as e:
            errors.append(f"Parse error: {e}")
            return ValidationResult(False, errors, warnings, None)
        
        # AST size check
        node_count = len(list(ast.walk(tree)))
        if node_count > self.max_ast_nodes:
            errors.append(f"Code too complex: {node_count} AST nodes > {self.max_ast_nodes}")
        
        # Security checks
        security_warnings = self._check_code_security(tree)
        warnings.extend(security_warnings)
        
        # Code quality checks
        quality_warnings = self._check_code_quality(tree, code)
        warnings.extend(quality_warnings)
        
        # Sanitize code
        sanitized_code = self._sanitize_code(code)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            sanitized_value=sanitized_code
        )
    
    def _check_code_security(self, tree: ast.AST) -> List[str]:
        """Check for potentially dangerous code patterns."""
        warnings = []
        
        for node in ast.walk(tree):
            # Check for dangerous imports
            if isinstance(node, ast.Import):
                for alias in node.names:
                    if alias.name in self.dangerous_imports:
                        warnings.append(f"Potentially dangerous import: {alias.name}")
            
            elif isinstance(node, ast.ImportFrom):
                if node.module in self.dangerous_imports:
                    warnings.append(f"Potentially dangerous import: {node.module}")
            
            # Check for dangerous function calls
            elif isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    if node.func.id in self.dangerous_functions:
                        warnings.append(f"Potentially dangerous function: {node.func.id}")
        
        return warnings
    
    def _check_code_quality(self, tree: ast.AST, code: str) -> List[str]:
        """Check for code quality issues."""
        warnings = []
        
        # Check for very long lines
        lines = code.splitlines()
        for i, line in enumerate(lines, 1):
            if len(line) > 120:
                warnings.append(f"Long line at {i}: {len(line)} characters")
        
        # Check for deeply nested structures
        max_depth = self._calculate_nesting_depth(tree)
        if max_depth > 6:
            warnings.append(f"Deep nesting detected: {max_depth} levels")
        
        # Check for too many functions/classes
        functions = [n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]
        classes = [n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]
        
        if len(functions) > 50:
            warnings.append(f"Many functions: {len(functions)}")
        if len(classes) > 20:
            warnings.append(f"Many classes: {len(classes)}")
        
        return warnings
    
    def _calculate_nesting_depth(self, tree: ast.AST) -> int:
        """Calculate maximum nesting depth of control structures."""
        def get_depth(node, current_depth=0):
            max_depth = current_depth
            
            for child in ast.iter_child_nodes(node):
                if isinstance(child, (ast.If, ast.For, ast.While, ast.With, ast.Try)):
                    child_depth = get_depth(child, current_depth + 1)
                    max_depth = max(max_depth, child_depth)
                else:
                    child_depth = get_depth(child, current_depth)
                    max_depth = max(max_depth, child_depth)
            
            return max_depth
        
        return get_depth(tree)
    
    def _sanitize_code(self, code: str) -> str:
        """Sanitize code for safe processing."""
        # Remove BOM
        if code.startswith('\ufeff'):
            code = code[1:]
        
        # Normalize line endings
        code = code.replace('\r\n', '\n').replace('\r', '\n')
        
        # Remove trailing whitespace
        lines = code.splitlines()
        sanitized_lines = [line.rstrip() for line in lines]
        
        # Ensure final newline
        sanitized_code = '\n'.join(sanitized_lines)
        if sanitized_code and not sanitized_code.endswith('\n'):
            sanitized_code += '\n'
        
        return sanitized_code
    
    def validate_python_version_compatibility(self, code: str, target_version: Tuple[int, int] = (3, 8)) -> ValidationResult:
        """
        Check if code is compatible with target Python version.
        
        Args:
            code: Python source code
            target_version: Target Python version tuple (major, minor)
            
        Returns:
            ValidationResult with compatibility information
        """
        errors = []
        warnings = []
        
        try:
            # Try parsing with different Python versions
            tree = ast.parse(code)
            
            # Check for version-specific features
            for node in ast.walk(tree):
                # Python 3.8+ features
                if isinstance(node, ast.NamedExpr) and target_version < (3, 8):
                    errors.append("Walrus operator (:=) requires Python 3.8+")
                
                # Python 3.9+ features  
                if hasattr(ast, 'unparse') and target_version < (3, 9):
                    # This is just an example - actual feature detection would be more complex
                    pass
                
                # Python 3.10+ features
                if isinstance(node, ast.Match) and target_version < (3, 10):
                    errors.append("Match statement requires Python 3.10+")
        
        except SyntaxError as e:
            errors.append(f"Syntax error: {e}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            sanitized_value=code if len(errors) == 0 else None
        )
