"""
FlowLens End-to-End Integration Tests

This module contains comprehensive integration tests that verify
the complete FlowLens workflow from code input to visualization output.
"""

import pytest
import tempfile
from pathlib import Path
from flowlens import FlowAnalyzer, Visualizer
from flowlens.core import <PERSON><PERSON><PERSON><PERSON>, CFGGenerator, ExecutionTracker, VariableMonitor
from flowlens.cli.main import cli
from click.testing import CliRunner


class TestEndToEndWorkflow:
    """Test complete FlowLens workflows."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.sample_code = '''
def fibonacci(n):
    """Calculate Fibonacci number."""
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def main():
    """Main function."""
    numbers = []
    for i in range(5):
        fib = fibonacci(i)
        numbers.append(fib)
        print(f"fib({i}) = {fib}")
    
    return numbers

if __name__ == "__main__":
    result = main()
    print(f"Results: {result}")
        '''
    
    def test_complete_analysis_workflow(self):
        """Test complete analysis workflow from code to results."""
        # Step 1: Parse code
        parser = ASTParser()
        parse_result = parser.parse_code(self.sample_code)
        
        assert parse_result is not None
        assert len(parse_result.functions) >= 2  # fibonacci, main
        assert len(parse_result.decision_points) >= 1  # if statement
        assert len(parse_result.loops) >= 1  # for loop
        
        # Step 2: Generate CFG
        cfg_generator = CFGGenerator()
        cfg = cfg_generator.generate_cfg(parse_result)
        
        assert cfg is not None
        assert len(cfg.nodes) > 0
        assert len(cfg.edges) > 0
        assert cfg.entry_node is not None
        
        # Step 3: Execute and track
        tracker = ExecutionTracker()
        execution_id = tracker.start_tracking(self.sample_code, cfg)
        execution_result = tracker.run_to_completion(execution_id)
        
        assert execution_result is not None
        assert execution_result.total_steps > 0
        assert execution_result.duration_ms > 0
        
        # Step 4: Monitor variables
        monitor = VariableMonitor()
        monitor.start_monitoring()
        variable_analysis = monitor.process_execution_result(execution_result)
        
        assert variable_analysis is not None
        assert variable_analysis['total_variables'] > 0
        assert 'heatmap_data' in variable_analysis
    
    def test_high_level_api_workflow(self):
        """Test high-level API workflow."""
        # Use high-level API
        analyzer = FlowAnalyzer()
        visualizer = Visualizer()
        
        # Analyze code
        result = analyzer.analyze_code(self.sample_code)
        
        assert result is not None
        assert result.source_code == self.sample_code
        assert len(result.functions) >= 2
        
        # Test summary
        summary = result.summary
        assert 'functions' in summary
        assert 'complexity_score' in summary
        assert 'parse_time_ms' in summary
    
    def test_file_based_workflow(self):
        """Test workflow with file input."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(self.sample_code)
            temp_file = Path(f.name)
        
        try:
            # Analyze file
            analyzer = FlowAnalyzer()
            result = analyzer.analyze_file(temp_file)
            
            assert result.filepath == str(temp_file)
            assert result.source_code == self.sample_code
            assert len(result.functions) >= 2
            
        finally:
            temp_file.unlink()
    
    def test_performance_requirements(self):
        """Test that performance requirements are met."""
        # Generate code with exactly 100 lines
        lines = []
        for i in range(50):
            lines.append(f"def func_{i}():")
            lines.append(f"    return {i}")
        
        code_100_lines = "\n".join(lines)
        
        # Test parsing performance
        parser = ASTParser()
        result = parser.parse_code(code_100_lines)
        
        # Should be under 200ms for 100 lines
        assert result.parse_time < 0.2, f"Parse time {result.parse_time:.3f}s exceeds 200ms requirement"
        assert result.total_lines == 100
        
        # Test CFG generation performance
        import time
        start_time = time.time()
        
        cfg_generator = CFGGenerator()
        cfg = cfg_generator.generate_cfg(result)
        
        cfg_time = time.time() - start_time
        assert cfg_time < 0.2, f"CFG generation time {cfg_time:.3f}s exceeds 200ms requirement"
    
    def test_accuracy_requirements(self):
        """Test execution path prediction accuracy."""
        # Use deterministic code for accuracy testing
        deterministic_code = '''
def test_function():
    x = 0
    for i in range(3):
        if i % 2 == 0:
            x += i
        else:
            x -= i
    return x

result = test_function()
        '''
        
        # Parse and generate CFG
        parser = ASTParser()
        parse_result = parser.parse_code(deterministic_code)
        
        cfg_generator = CFGGenerator()
        cfg = cfg_generator.generate_cfg(parse_result)
        
        # Execute and track
        tracker = ExecutionTracker()
        execution_id = tracker.start_tracking(deterministic_code, cfg)
        execution_result = tracker.run_to_completion(execution_id)
        
        # Verify execution completed successfully
        assert execution_result.final_state.value in ['completed', 'error']
        assert execution_result.total_steps > 0
        
        # For this simple code, we should have high accuracy
        # (In a real implementation, you'd compare predicted vs actual paths)
        assert len(execution_result.steps) > 5  # Should have multiple execution steps


class TestCLIIntegration:
    """Test CLI integration and commands."""
    
    def setup_method(self):
        """Set up CLI test fixtures."""
        self.runner = CliRunner()
        self.sample_code = '''
def hello_world():
    print("Hello, World!")
    return True

x = 5
y = x + 10
print(f"Result: {y}")
        '''
    
    def test_cli_version(self):
        """Test CLI version command."""
        result = self.runner.invoke(cli, ['--version'])
        assert result.exit_code == 0
        assert 'FlowLens version' in result.output
    
    def test_cli_help(self):
        """Test CLI help command."""
        result = self.runner.invoke(cli, ['--help'])
        assert result.exit_code == 0
        assert 'FlowLens' in result.output
        assert 'analyze' in result.output
        assert 'step' in result.output
        assert 'graph' in result.output
        assert 'heatmap' in result.output
    
    def test_cli_analyze_command(self):
        """Test CLI analyze command."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(self.sample_code)
            temp_file = Path(f.name)
        
        try:
            result = self.runner.invoke(cli, ['analyze', str(temp_file)])
            assert result.exit_code == 0
            
        finally:
            temp_file.unlink()
    
    def test_cli_analyze_with_output(self):
        """Test CLI analyze command with output file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(self.sample_code)
            temp_file = Path(f.name)
        
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as out_f:
            output_file = Path(out_f.name)
        
        try:
            result = self.runner.invoke(cli, [
                'analyze', str(temp_file), 
                '--output', str(output_file),
                '--format', 'json'
            ])
            assert result.exit_code == 0
            assert output_file.exists()
            
            # Verify output file contains valid JSON
            import json
            with open(output_file) as f:
                data = json.load(f)
            assert 'summary' in data
            
        finally:
            temp_file.unlink()
            if output_file.exists():
                output_file.unlink()
    
    def test_cli_graph_command(self):
        """Test CLI graph command."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(self.sample_code)
            temp_file = Path(f.name)
        
        try:
            result = self.runner.invoke(cli, ['graph', str(temp_file)])
            # May fail due to missing display, but should not crash
            assert result.exit_code in [0, 1]
            
        finally:
            temp_file.unlink()
    
    def test_cli_step_command(self):
        """Test CLI step command."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(self.sample_code)
            temp_file = Path(f.name)
        
        try:
            # Test auto-run mode
            result = self.runner.invoke(cli, [
                'step', str(temp_file), 
                '--auto-run',
                '--max-steps', '10'
            ])
            # Should complete without user interaction
            assert result.exit_code == 0
            
        finally:
            temp_file.unlink()


class TestErrorHandling:
    """Test error handling and edge cases."""
    
    def test_invalid_python_syntax(self):
        """Test handling of invalid Python syntax."""
        invalid_code = '''
def broken_function(
    print("This is broken")
        '''
        
        parser = ASTParser()
        with pytest.raises(SyntaxError):
            parser.parse_code(invalid_code)
    
    def test_empty_code(self):
        """Test handling of empty code."""
        parser = ASTParser()
        result = parser.parse_code("")
        
        assert result.total_lines == 0
        assert len(result.functions) == 0
        assert len(result.classes) == 0
    
    def test_nonexistent_file(self):
        """Test handling of nonexistent files."""
        analyzer = FlowAnalyzer()
        
        with pytest.raises(FileNotFoundError):
            analyzer.analyze_file("nonexistent_file.py")
    
    def test_large_file_handling(self):
        """Test handling of large files."""
        # Generate large code (but not too large for CI)
        large_code = "\n".join([f"x_{i} = {i}" for i in range(1000)])
        
        parser = ASTParser()
        result = parser.parse_code(large_code)
        
        assert result.total_lines == 1000
        assert len(result.variables) == 1000
    
    def test_complex_code_structures(self):
        """Test handling of complex code structures."""
        complex_code = '''
class ComplexClass:
    def __init__(self):
        self.data = {}
    
    def method_with_nested_loops(self):
        for i in range(10):
            for j in range(10):
                if i % 2 == 0:
                    if j % 2 == 0:
                        try:
                            self.data[f"{i}_{j}"] = i * j
                        except Exception as e:
                            print(f"Error: {e}")
                        finally:
                            pass
                    else:
                        continue
                else:
                    break
        
        return self.data

def recursive_function(n, depth=0):
    if depth > 100:  # Prevent infinite recursion
        return n
    
    if n <= 1:
        return 1
    else:
        return recursive_function(n-1, depth+1) + recursive_function(n-2, depth+1)

async def async_function():
    await some_async_operation()
    return "done"

@decorator
def decorated_function():
    pass
        '''
        
        parser = ASTParser()
        result = parser.parse_code(complex_code)
        
        assert len(result.classes) >= 1
        assert len(result.functions) >= 3
        assert len(result.decision_points) >= 3
        assert len(result.loops) >= 2
        assert len(result.exception_handlers) >= 1
