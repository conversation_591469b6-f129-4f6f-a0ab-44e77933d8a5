"""
FlowLens File Handler

This module provides file handling and I/O operations for FlowLens,
including Python code loading, validation, and preprocessing.

Features:
- Safe file loading with encoding detection
- Python code validation and syntax checking
- File type detection and filtering
- Batch file processing capabilities
"""

import os
import sys
import ast
import chardet
from pathlib import Path
from typing import List, Dict, Optional, Union, Tuple, Generator
from dataclasses import dataclass
import mimetypes


@dataclass
class FileInfo:
    """Information about a loaded file."""
    
    path: Path
    size_bytes: int
    encoding: str
    line_count: int
    is_valid_python: bool
    syntax_error: Optional[str] = None
    
    @property
    def size_kb(self) -> float:
        """Get file size in KB."""
        return self.size_bytes / 1024
    
    @property
    def size_mb(self) -> float:
        """Get file size in MB."""
        return self.size_bytes / (1024 * 1024)


class FileHandler:
    """
    File handling utilities for FlowLens.
    
    Provides safe and efficient file operations with proper error handling
    and encoding detection.
    """
    
    def __init__(self, max_file_size_mb: float = 10.0):
        self.max_file_size_mb = max_file_size_mb
        self.max_file_size_bytes = int(max_file_size_mb * 1024 * 1024)
    
    def load_file(self, filepath: Union[str, Path]) -> Tuple[str, FileInfo]:
        """
        Load a file with automatic encoding detection.
        
        Args:
            filepath: Path to the file to load
            
        Returns:
            Tuple of (file_content, file_info)
            
        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If file is too large or not a Python file
            UnicodeDecodeError: If file cannot be decoded
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"File not found: {filepath}")
        
        if not filepath.is_file():
            raise ValueError(f"Path is not a file: {filepath}")
        
        # Check file size
        file_size = filepath.stat().st_size
        if file_size > self.max_file_size_bytes:
            raise ValueError(f"File too large: {file_size / (1024*1024):.1f}MB > {self.max_file_size_mb}MB")
        
        # Detect encoding
        encoding = self._detect_encoding(filepath)
        
        # Load content
        try:
            with open(filepath, 'r', encoding=encoding) as f:
                content = f.read()
        except UnicodeDecodeError as e:
            # Try with fallback encodings
            for fallback_encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    with open(filepath, 'r', encoding=fallback_encoding) as f:
                        content = f.read()
                    encoding = fallback_encoding
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError(f"Could not decode file: {filepath}") from e
        
        # Validate Python syntax
        is_valid_python, syntax_error = self._validate_python_syntax(content, str(filepath))
        
        # Create file info
        file_info = FileInfo(
            path=filepath,
            size_bytes=file_size,
            encoding=encoding,
            line_count=len(content.splitlines()),
            is_valid_python=is_valid_python,
            syntax_error=syntax_error,
        )
        
        return content, file_info
    
    def _detect_encoding(self, filepath: Path) -> str:
        """Detect file encoding using chardet."""
        try:
            with open(filepath, 'rb') as f:
                raw_data = f.read(min(32768, filepath.stat().st_size))  # Read up to 32KB
            
            result = chardet.detect(raw_data)
            encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0.0)
            
            # Use utf-8 if confidence is low
            if confidence < 0.7:
                encoding = 'utf-8'
            
            return encoding
        except Exception:
            return 'utf-8'  # Fallback to utf-8
    
    def _validate_python_syntax(self, content: str, filename: str = "<string>") -> Tuple[bool, Optional[str]]:
        """Validate Python syntax."""
        try:
            ast.parse(content, filename=filename)
            return True, None
        except SyntaxError as e:
            error_msg = f"Line {e.lineno}: {e.msg}"
            return False, error_msg
        except Exception as e:
            return False, str(e)
    
    def find_python_files(self, directory: Union[str, Path], recursive: bool = True) -> List[Path]:
        """
        Find all Python files in a directory.
        
        Args:
            directory: Directory to search
            recursive: Whether to search recursively
            
        Returns:
            List of Python file paths
        """
        directory = Path(directory)
        
        if not directory.exists():
            raise FileNotFoundError(f"Directory not found: {directory}")
        
        if not directory.is_dir():
            raise ValueError(f"Path is not a directory: {directory}")
        
        pattern = "**/*.py" if recursive else "*.py"
        python_files = list(directory.glob(pattern))
        
        # Filter out common non-source directories
        excluded_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
        
        filtered_files = []
        for file_path in python_files:
            # Check if any parent directory is in excluded set
            if not any(part in excluded_dirs for part in file_path.parts):
                filtered_files.append(file_path)
        
        return sorted(filtered_files)
    
    def batch_load_files(self, filepaths: List[Union[str, Path]]) -> Generator[Tuple[str, FileInfo], None, None]:
        """
        Load multiple files in batch with error handling.
        
        Args:
            filepaths: List of file paths to load
            
        Yields:
            Tuples of (file_content, file_info) for successfully loaded files
        """
        for filepath in filepaths:
            try:
                content, file_info = self.load_file(filepath)
                yield content, file_info
            except Exception as e:
                # Create error file info
                error_info = FileInfo(
                    path=Path(filepath),
                    size_bytes=0,
                    encoding="unknown",
                    line_count=0,
                    is_valid_python=False,
                    syntax_error=str(e),
                )
                yield "", error_info


class CodeLoader:
    """
    Specialized loader for Python code with additional validation.
    """
    
    def __init__(self, file_handler: Optional[FileHandler] = None):
        self.file_handler = file_handler or FileHandler()
    
    def load_python_file(self, filepath: Union[str, Path]) -> Tuple[str, FileInfo]:
        """
        Load a Python file with additional validation.
        
        Args:
            filepath: Path to Python file
            
        Returns:
            Tuple of (source_code, file_info)
            
        Raises:
            ValueError: If file is not a valid Python file
        """
        filepath = Path(filepath)
        
        # Check file extension
        if filepath.suffix.lower() not in ['.py', '.pyw']:
            raise ValueError(f"File is not a Python file: {filepath}")
        
        content, file_info = self.file_handler.load_file(filepath)
        
        if not file_info.is_valid_python:
            raise ValueError(f"Invalid Python syntax: {file_info.syntax_error}")
        
        return content, file_info
    
    def load_code_string(self, code: str, validate: bool = True) -> str:
        """
        Load and optionally validate a Python code string.
        
        Args:
            code: Python source code
            validate: Whether to validate syntax
            
        Returns:
            The code string (potentially normalized)
            
        Raises:
            SyntaxError: If code has invalid syntax and validate=True
        """
        if validate:
            try:
                ast.parse(code)
            except SyntaxError as e:
                raise SyntaxError(f"Invalid Python syntax: {e}")
        
        return code
    
    def preprocess_code(self, code: str) -> str:
        """
        Preprocess Python code for analysis.
        
        Args:
            code: Raw Python source code
            
        Returns:
            Preprocessed code
        """
        # Remove BOM if present
        if code.startswith('\ufeff'):
            code = code[1:]
        
        # Normalize line endings
        code = code.replace('\r\n', '\n').replace('\r', '\n')
        
        # Ensure code ends with newline
        if code and not code.endswith('\n'):
            code += '\n'
        
        return code
    
    def extract_imports(self, code: str) -> List[str]:
        """
        Extract import statements from Python code.
        
        Args:
            code: Python source code
            
        Returns:
            List of import statements
        """
        try:
            tree = ast.parse(code)
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(f"import {alias.name}")
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        imports.append(f"from {module} import {alias.name}")
            
            return imports
        except SyntaxError:
            return []
    
    def get_code_statistics(self, code: str) -> Dict[str, int]:
        """
        Get basic statistics about Python code.
        
        Args:
            code: Python source code
            
        Returns:
            Dictionary with code statistics
        """
        lines = code.splitlines()
        
        stats = {
            "total_lines": len(lines),
            "blank_lines": sum(1 for line in lines if not line.strip()),
            "comment_lines": sum(1 for line in lines if line.strip().startswith('#')),
            "code_lines": 0,
        }
        
        stats["code_lines"] = stats["total_lines"] - stats["blank_lines"] - stats["comment_lines"]
        
        try:
            tree = ast.parse(code)
            stats.update({
                "functions": len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]),
                "classes": len([n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]),
                "imports": len([n for n in ast.walk(tree) if isinstance(n, (ast.Import, ast.ImportFrom))]),
            })
        except SyntaxError:
            stats.update({
                "functions": 0,
                "classes": 0,
                "imports": 0,
            })
        
        return stats
