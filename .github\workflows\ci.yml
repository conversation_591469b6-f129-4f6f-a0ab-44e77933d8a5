name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12']
        exclude:
          # Reduce matrix size for faster CI
          - os: windows-latest
            python-version: '3.9'
          - os: macos-latest
            python-version: '3.9'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y graphviz graphviz-dev

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install graphviz

    - name: Install system dependencies (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        choco install graphviz

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Lint with flake8
      run: |
        flake8 flowlens tests --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 flowlens tests --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Type check with mypy
      run: |
        mypy flowlens --ignore-missing-imports

    - name: Format check with black
      run: |
        black --check flowlens tests

    - name: Import sort check with isort
      run: |
        isort --check-only flowlens tests

    - name: Security check with bandit
      run: |
        bandit -r flowlens -f json -o bandit-report.json || true

    - name: Test with pytest
      run: |
        pytest tests/ -v --cov=flowlens --cov-report=xml --cov-report=html --cov-report=term

    - name: Performance tests
      run: |
        pytest tests/ -k "performance" -v --benchmark-only --benchmark-json=benchmark.json || true

    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.python-version == '3.11'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.os }}-${{ matrix.python-version }}
        path: |
          htmlcov/
          bandit-report.json
          benchmark.json

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: |
        python -m build

    - name: Check package
      run: |
        twine check dist/*

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  integration-test:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y graphviz graphviz-dev

    - name: Install FlowLens
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Run integration tests
      run: |
        pytest tests/integration/ -v

    - name: Test CLI commands
      run: |
        flowlens --version
        flowlens --help
        flowlens analyze tests/fixtures/sample_code.py --format json
        flowlens graph tests/fixtures/sample_code.py --output test_graph.png
        flowlens heatmap tests/fixtures/sample_code.py --output test_heatmap.png

    - name: Upload integration test artifacts
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-outputs
        path: |
          test_graph.png
          test_heatmap.png
