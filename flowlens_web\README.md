# FlowLens Web Application

A modern web interface for FlowLens code visualization and analysis. This web application provides all the functionality of the FlowLens CLI tool through an intuitive browser-based interface.

## Features

- **Interactive Code Editor**: Syntax-highlighted Python code editor with CodeMirror
- **File Upload**: Upload Python files directly from your computer
- **Real-time Analysis**: Instant code analysis with comprehensive metrics
- **Multiple Visualizations**: Control flow graphs, Mermaid diagrams, and variable heatmaps
- **Interactive Diagrams**: Zoom, pan, and export capabilities for all visualizations
- **Freemium Model**: Tier-based limitations matching the CLI tool
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## Quick Start

### Local Development

1. **Install Dependencies**:
   ```bash
   # Install FlowLens core package
   pip install -e .
   
   # Install web application dependencies
   pip install -r flowlens_web/requirements.txt
   ```

2. **Start Development Server**:
   ```bash
   python flowlens_web/start_dev.py
   ```

3. **Open Browser**:
   Navigate to `http://localhost:8000`

### Docker Deployment

1. **Build and Run with <PERSON>er Compose**:
   ```bash
   cd flowlens_web
   docker-compose up --build
   ```

2. **Access Application**:
   Open `http://localhost:8000` in your browser

## API Endpoints

### Core Endpoints

- `GET /` - Main web interface
- `GET /api/health` - Health check
- `POST /api/analyze` - Analyze Python code
- `POST /api/visualize` - Generate visualizations
- `POST /api/upload` - Upload Python files
- `GET /api/tier` - Get tier information

### API Documentation

- Interactive API docs: `http://localhost:8000/api/docs`
- ReDoc documentation: `http://localhost:8000/api/redoc`

## Usage Examples

### Code Analysis

1. **Upload a File**: Click "Choose File" and select a Python file
2. **Or Paste Code**: Use the code editor to paste Python code
3. **Select Analysis Type**: Choose from:
   - Code Analysis (metrics and structure)
   - Control Flow Graph (PNG/SVG visualization)
   - Mermaid Diagram (interactive flowchart)
   - Variable Heatmap (execution analysis)
4. **Configure Options**: Set format, theme, and direction
5. **Analyze**: Click "Analyze Code" to generate results

### Visualization Options

- **Formats**: PNG, SVG, PDF, Mermaid text, Interactive HTML
- **Themes**: Default, Dark, Forest, Base
- **Directions**: Top-Down, Left-Right, Bottom-Top, Right-Left
- **Function Filtering**: Analyze specific functions only

## Freemium Features

### Free Tier
- Analysis up to 100 lines of code
- PNG export only
- Basic visualizations
- Limited usage tracking

### Pro Tier ($19/month)
- Unlimited code analysis
- All export formats (SVG, PDF, HTML)
- Advanced visualizations
- Performance profiling
- Priority support

### Team Tier ($49/user/month)
- All Pro features
- Team collaboration
- Usage analytics
- Custom branding
- API access

## Development

### Project Structure

```
flowlens_web/
├── main.py              # FastAPI application
├── templates/           # Jinja2 templates
│   └── index.html      # Main web interface
├── static/             # Static assets
│   ├── css/
│   │   └── style.css   # Application styles
│   └── js/
│       └── app.js      # Frontend JavaScript
├── requirements.txt    # Python dependencies
├── Dockerfile         # Docker configuration
├── docker-compose.yml # Docker Compose setup
├── start_dev.py       # Development server script
└── README.md          # This file
```

### Adding New Features

1. **Backend**: Add new endpoints in `main.py`
2. **Frontend**: Update `templates/index.html` and `static/js/app.js`
3. **Styling**: Modify `static/css/style.css`
4. **Dependencies**: Update `requirements.txt`

### Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest flowlens_web/tests/
```

## Deployment

### Production Deployment

1. **Environment Variables**:
   ```bash
   export ENVIRONMENT=production
   export LOG_LEVEL=info
   export SECRET_KEY=your-secret-key
   ```

2. **Using Docker**:
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

3. **Using Systemd** (Linux):
   ```bash
   # Create service file
   sudo cp flowlens-web.service /etc/systemd/system/
   sudo systemctl enable flowlens-web
   sudo systemctl start flowlens-web
   ```

### Reverse Proxy (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Security Considerations

- **File Upload**: Only Python files are accepted
- **Code Execution**: Code is parsed but not executed by default
- **Rate Limiting**: Implement rate limiting for production
- **CORS**: Configure CORS settings appropriately
- **Authentication**: Add user authentication for production use

## Performance

- **Caching**: Redis integration for session storage
- **Database**: PostgreSQL for user data and analytics
- **CDN**: Use CDN for static assets in production
- **Monitoring**: Add application monitoring and logging

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure FlowLens core package is installed
2. **Port Conflicts**: Change port in `start_dev.py` or docker-compose
3. **Permission Errors**: Check file permissions and user access
4. **Memory Issues**: Increase Docker memory limits for large files

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=debug
python flowlens_web/start_dev.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/HectorTa1989/FlowLens/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/HectorTa1989/FlowLens/discussions)
- 📖 Documentation: [docs.flowlens.com](https://docs.flowlens.com)
